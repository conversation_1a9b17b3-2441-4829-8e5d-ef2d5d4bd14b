<!doctype html>

<title>CodeMirror: Real-world Uses</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="docs.css">

<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Real-world uses</a>
  </ul>
</div>

<article>

<h2>CodeMirror real-world uses</h2>

    <p>Create a <a href="https://github.com/codemirror/codemirror5">pull
    request</a> if you'd like your project to be added to this list.</p>

    <ul>
      <li><a href="https://www.adaface.com/pair-pro">Adaface PairPro</a> (Shared code editor with compiler and video conferencing)</li>
      <li><a href="http://brackets.io">Adobe Brackets</a> (code editor)</li>
      <li><a href="http://adnuntius.com">Adnuntius</a> (used for in-browser code editing and version history)</li>
      <li><a href="http://alm.tools">ALM Tools</a> (TypeScript powered IDE)</li>
      <li><a href="http://amber-lang.net/">Amber</a> (JavaScript-based Smalltalk system)</li>
      <li><a href="http://apeye.org/">APEye</a> (tool for testing &amp; documenting APIs)</li>
      <li><a href="https://github.com/google/appengine-codiad">Appengine Codiad</a></li>
      <li><a href="https://chrome.google.com/webstore/detail/better-text-viewer/lcaidopdffhfemoefoaadecppnjdknkc">Better Text Viewer</a> (plain text reader app for Chrome)</li>
      <li><a href="http://blog.bitbucket.org/2013/05/14/edit-your-code-in-the-cloud-with-bitbucket/">Bitbucket</a> (code hosting)</li>
      <li><a href="https://blogger.googleblog.com/2013/04/improvements-to-blogger-template-html.html">Blogger's theme editor</a></li>
      <li><a href="http://bluegriffon.org/">BlueGriffon</a> (HTML editor)</li>
      <li><a href="https://bnfplayground.pauliankline.com/">BNF Playground</a> (grammar workbench)</li>
      <li><a href="https://github.com/isdampe/BosonEditorExperimental">Boson Editor</a> (code editor)</li>
      <li><a href="http://cargocollective.com/">Cargo Collective</a> (creative publishing platform)</li>
      <li><a href="https://developers.google.com/chrome-developer-tools/">Chrome DevTools</a></li>
      <li><a href="http://clickhelp.co/">ClickHelp</a> (technical writing tool)</li>
      <li><a href="https://encap.github.io/clone-it/">Clone-It</a> (HTML & CSS learning game)</li>
      <li><a href="https://cloudcmd.io">Cloud Commander</a> (filemanager for the web)</li>
      <li><a href="https://electronjs.org/apps/colon">Colon</a> (A flexible text editor or IDE)</li>
      <li><a href="http://code.world/">CodeWorld</a> (Haskell playground)</li>
      <li><a href="http://complete-ly.appspot.com/playground/code.playground.html">Complete.ly playground</a></li>
      <li><a href="https://codeanywhere.com/">Codeanywhere</a> (multi-platform cloud editor)</li>
      <li><a href="http://drupal.org/project/cpn">Code per Node</a> (Drupal module)</li>
      <li><a href="https://codebitt.com/">CodeBitt</a> (Code snippet sharing)</li>
      <li><a href="http://www.codebugapp.com/">Codebug</a> (PHP Xdebug front-end)</li>
      <li><a href="http://codefights.com/">CodeFights</a> (practice programming)</li>
      <li><a href="https://github.com/angelozerr/CodeMirror-Eclipse">CodeMirror Eclipse</a> (embed CM in Eclipse)</li>
      <li><a href="http://emmet.io/blog/codemirror-movie/">CodeMirror movie</a> (scripted editing demos)</li>
      <li><a href="https://github.com/haoranyu/codemirror-record/">CodeMirror Record</a> (codemirror activity recording and playback)</li>
      <li><a href="http://code.google.com/p/codemirror2-gwt/">CodeMirror2-GWT</a> (Google Web Toolkit wrapper)</li>
      <li><a href="http://www.crunchzilla.com/code-monster">Code Monster</a> & <a href="http://www.crunchzilla.com/code-maven">Code Maven</a> (learning environment)</li>
      <li><a href="http://codepen.io">Codepen</a> (gallery of animations)</li>
      <li><a href="https://github.com/pepstock-org/Coderba">Coderba</a> Google Web Toolkit (GWT) wrapper</li>
      <li><a href="https://coderpad.io/">Coderpad</a> (interviewing tool)</li>
      <li><a href="https:/coderush.xyz/">CodeRush</a> typing speed test for programmers</li>
      <li><a href="http://sasstwo.codeschool.com/levels/1/challenges/1">Code School</a> (online tech learning environment)</li>
      <li><a href="http://code-snippets.bungeshea.com/">Code Snippets</a> (WordPress snippet management plugin)</li>
      <li><a href="http://antonmi.github.io/code_together/">Code together</a> (collaborative editing)</li>
      <li><a href="https://www.codevolve.com/">Codevolve</a> (programming lessons as-a-service)</li>
      <li><a href="http://www.codezample.com">CodeZample</a> (code snippet sharing)</li>
      <li><a href="http://codio.com">Codio</a> (Web IDE)</li>
      <li><a href="https://www.codiva.io/">Codiva.io</a> (Online Java Compiler and IDE with auto-completion and error highlighting)</li>
      <li><a href="http://www.communitycodecamp.com/">Community Code Camp</a> (code snippet sharing)</li>
      <li><a href="http://www.compilejava.net/">compilejava.net</a> (online Java sandbox)</li>
      <li><a href="http://www.ckwnc.com/">CKWNC</a> (UML editor)</li>
      <li><a href="http://www.crossui.com/">CrossUI</a> (cross-platform UI builder)</li>
      <li><a href="http://rsnous.com/cruncher/">Cruncher</a> (notepad with calculation features)</li>
      <li><a href="http://www.crudzilla.com/">Crudzilla</a> (self-hosted web IDE)</li>
      <li><a href="http://cssdeck.com/">CSSDeck</a> (CSS showcase)</li>
      <li><a href="http://ireneros.com/deck/deck.js-codemirror/introduction/#textarea-code">Deck.js integration</a> (slides with editors)</li>
      <li><a href="http://www.dbninja.com">DbNinja</a> (MySQL access interface)</li>
      <li><a href="http://www.ecsspert.com/">eCSSpert</a> (CSS demos and experiments)</li>
      <li><a href="https://edabit.com">Edabit</a> (coding challenges)</li>
      <li><a href="http://elm-lang.org/Examples.elm">Elm language examples</a></li>
      <li><a href="http://eloquentjavascript.net/chapter1.html">Eloquent JavaScript</a> (book)</li>
      <li><a href="http://emmet.io">Emmet</a> (fast XML editing)</li>
      <li><a href="https://github.com/espruino/EspruinoWebIDE">Espruino Web IDE</a> (Chrome App for writing code on Espruino devices)</li>
      <li><a href="https://exlskills.com/learn-en/talent/interviews">EXLskills Live Interviews</a></li>
      <li><a href="http://www.fastfig.com/">Fastfig</a> (online computation/math tool)</li>
      <li><a href="https://metacpan.org/module/Farabi">Farabi</a> (modern Perl IDE)</li>
      <li><a href="http://blog.pamelafox.org/2012/02/interactive-html5-slides-with-fathomjs.html">FathomJS integration</a> (slides with editors, again)</li>
      <li><a href="http://fiddlesalad.com/">Fiddle Salad</a> (web development environment)</li>
      <li><a href="https://github.com/simogeo/Filemanager">Filemanager</a></li>
      <li><a href="https://hacks.mozilla.org/2013/11/firefox-developer-tools-episode-27-edit-as-html-codemirror-more/">Firefox Developer Tools</a></li>
      <li><a href="http://www.firepad.io">Firepad</a> (collaborative text editor)</li>
      <li><a href="https://gerritcodereview.com/">Gerrit</a>'s diff view and inline editor</li>
      <li><a href="https://github.com/maks/git-crx">Git Crx</a> (Chrome App for browsing local git repos)</li>
      <li><a href="https://github.com/github/android">GitHub's Android app</a></li>
      <li><a href="https://github.com/">GitHub</a>'s in-browser edit feature</li>
      <li><a href="https://glitch.com/">Glitch</a> (community-driven app building)</li>
      <li><a href="http://tour.golang.org">Go language tour</a></li>
      <li><a href="https://script.google.com/">Google Apps Script</a></li>
      <li><a href="http://web.uvic.ca/~siefkenj/graphit/graphit.html">Graphit</a> (function graphing)</li>
      <li><a href="https://graviton.netlify.app/">Graviton Editor</a> (Cross-platform and modern-looking code editor)</li>
      <li><a href="https://hackmd.io">HackMD</a> (Realtime collaborative markdown notes on all platforms)</li>
      <li><a href="http://www.handcraft.com/">Handcraft</a> (HTML prototyping)</li>
      <li><a href="http://hawkee.com/">Hawkee</a></li>
      <li><a href="http://try.haxe.org">Haxe</a> (Haxe Playground) </li>
      <li><a href="http://haxpad.com/">HaxPad</a> (editor for Win RT)</li>
      <li><a href="http://megafonweblab.github.com/histone-javascript/">Histone template engine playground</a></li>
      <li><a href="http://www.homegenie.it/docs/automation_getstarted.php">Homegenie</a> (home automation server)</li>
      <li><a href="http://icecoder.net">ICEcoder</a> (web IDE)</li>
      <li><a href="https://innovay.app/formatter/html">Innovay Web Tools</a> (HTML, JS, CSS code beautifier)</li>
      <li><a href="https://www.intervue.io">Intervue</a> (Pair programming for interviews)</li>
      <li><a href="http://ipython.org/">IPython</a> (interactive computing shell)</li>
      <li><a href="https://joelpinheiro.github.io/itrading/">iTrading</a> (Algorithmic Trading)</li>
      <li><a href="http://i-mos.org/imos/">i-MOS</a> (modeling and simulation platform)</li>
      <li><a href="http://www.janvas.com/">Janvas</a> (vector graphics editor)</li>
      <li><a href="https://code.wetrafa.xyz/">JdBEdit</a> (web IDE)</li>
      <li><a href="http://extensions.joomla.org/extensions/edition/editors/8723">Joomla plugin</a></li>
      <li><a href="http://jqfundamentals.com/">jQuery fundamentals</a> (interactive tutorial)</li>
      <li><a href="http://jsbin.com">jsbin.com</a> (JS playground)</li>
      <li><a href="http://tool.jser.com/preprocessor">JSER preprocessor</a></li>
      <li><a href="https://github.com/kucherenko/jscpd">jscpd</a> (code duplication detector)</li>
      <li><a href="http://jsfiddle.net">JSFiddle</a> (another JS playground)</li>
      <li><a href="http://www.jshint.com/">JSHint</a> (JS linter)</li>
      <li><a href="http://jumpseller.com/">Jumpseller</a> (online store builder)</li>
      <li><a href="http://kl1p.com/cmtest/1">kl1p</a> (paste service)</li>
      <li><a href="http://www.kodhus.com/kodity/">Kodit</a></li>
      <li><a href="http://kodtest.com/">Kodtest</a> (HTML/JS/CSS playground)</li>
      <li><a href="http://try.kotlinlang.org">Kotlin</a> (web-based mini-IDE for Kotlin)</li>
      <li><a href="http://lighttable.com/">Light Table</a> (experimental IDE)</li>
      <li><a href="http://liveweave.com/">Liveweave</a> (HTML/CSS/JS scratchpad)</li>
      <li><a href="https://liveuml.com/">LiveUML</a> (PlantUML online editor)</li>
      <li><a href="https://github.com/TuvaLabs/markdown-delight-editor">Markdown Delight Editor</a> (extensible markdown editor polymer component)</li>
      <li><a href="http://marklighteditor.com/">Marklight editor</a> (lightweight markup editor)</li>
      <li><a href="https://www.mediawiki.org/wiki/Extension:CodeMirror">MediaWiki extension</a> (wiki engine)</li>
      <li><a href="http://www.mergely.com/">Mergely</a> (interactive diffing)</li>
      <li><a href="http://www.iunbug.com/mihtool">MIHTool</a> (iOS web-app debugging tool)</li>
      <li><a href="http://mscgen.js.org/index.html">mscgen_js</a> (online sequence chart editor)</li>
      <li><a href="http://mvcplayground.apphb.com/">MVC Playground</a></li>
      <li><a href="http://www.navigatecms.com">Navigate CMS</a></li>
      <li><a href="https://github.com/soliton4/nodeMirror">nodeMirror</a> (IDE project)</li>
      <li><a href="https://notex.ch">NoTex</a> (rST authoring)</li>
      <li><a href="https://nteract.io">nteract</a> (interactive literate coding notebook)</li>
      <li><a href="http://oakoutliner.com">Oak</a> (online outliner)</li>
      <li><a href="http://www.greycampus.com/opencampus">OpenCampus</a></li>
      <li><a href="http://clrhome.org/asm/">ORG</a> (z80 assembly IDE)</li>
      <li><a href="https://github.com/mamacdon/orion-codemirror">Orion-CodeMirror integration</a> (running CodeMirror modes in Orion)</li>
      <li><a href="https://www.overleaf.com/">Overleaf</a> (Collaborative LaTeX Editor)</li>
      <li><a href="http://paperjs.org/">Paper.js</a> (graphics scripting)</li>
      <li><a href="http://pharaoh.js.org/">Pharaoh</a> (browser &amp; desktop editor for the classroom)</li>
      <li><a href="http://prinbit.com/">PrinBit</a> (collaborative coding tool)</li>
      <li><a href="https://www.pramp.com/ref/codemirror">Pramp</a> (free platform to practice mock interviews and coding problems)</li>
      <li><a href="http://prose.io/">Prose.io</a> (github content editor)</li>
      <li><a href="https://pypi.python.org/pypi/PubliForge/">PubliForge</a> (online publishing system)</li>
      <li><a href="http://www.puzzlescript.net/">Puzzlescript</a> (puzzle game engine)</li>
      <li><a href="https://chrome.google.com/webstore/detail/quantum/hmnlklahndgbhdoclhdnoafhafbhmnkm?hl=en-US">Quantum</a> (code editor for Chrome OS)</li>
      <li><a href="http://ariya.ofilabs.com/2011/09/hybrid-webnative-desktop-codemirror.html">Qt+Webkit integration</a> (building a desktop CodeMirror app)</li>
      <li><a href="http://www.quivive-file-manager.com">Quivive File Manager</a></li>
      <li><a href="https://racktables.org">RackTables</a> (data centre resources manager)</li>
      <li><a href="http://rascalmicro.com/docs/basic-tutorial-getting-started.html">Rascal</a> (tiny computer)</li>
      <li><a href="https://www.realtime.io/">RealTime.io</a> (Internet-of-Things infrastructure)</li>
      <li><a href="http://refork.com/">Refork</a> (animation demo gallery and sharing)</li>
      <li><a href="http://sagecell.sagemath.org">SageMathCell</a> (interactive mathematical software)</li>
      <li><a href="https://sass2css.herokuapp.com/">SASS2CSS</a> (SASS, SCSS or LESS to CSS converter and CSS beautifier)</li>
      <li><a href="https://cloud.sagemath.com/">SageMathCloud</a> (interactive mathematical software environment)</li>
      <li><a href="https://github.com/szekelymilan/salvare">salvare</a> (real-time collaborative code editor)</li>
      <li><a href="https://chrome.google.com/webstore/detail/servephp/mnpikomdchjhkhbhmbboehfdjkobbfpo">ServePHP</a> (PHP code testing in Chrome dev tools)</li>
      <li><a href="http://scala-lang.org/blog/2017/02/20/introducing-scastie.html">Scastie</a> (Scala playground)</li>
      <li><a href="https://www.shadertoy.com/">Shadertoy</a> (shader sharing)</li>
      <li><a href="http://www.sketchpatch.net/labs/livecodelabIntro.html">sketchPatch Livecodelab</a></li>
      <li><a href="http://www.skulpt.org/">Skulpt</a> (in-browser Python environment)</li>
      <li><a href="https://www.sourcelair.com">SourceLair</a> (in-browser IDE for Django, Node.js, PHP and HTML5)</li>
      <li><a href="http://snaptomato.appspot.com/editor.html">Snap Tomato</a> (HTML editing/testing page)</li>
      <li><a href="http://snippets.pro/">Snippets.pro</a> (code snippet sharing)</li>
      <li><a href="http://www.solidshops.com/">SolidShops</a> (hosted e-commerce platform)</li>
      <li><a href="http://www.cemetech.net/sc/">SourceCoder 3</a> (online calculator IDE and editor)</li>
      <li><a href="http://sqlfiddle.com">SQLFiddle</a> (SQL playground)</li>
      <li><a href="http://www.subte.org/page/programar-ta-te-ti-online/">SubTe</a> (AI bot programming environment)</li>
      <li><a href="http://xuanji.appspot.com/isicp/">Structure and Interpretation of Computer Programs</a>, Interactive Version</li>
      <li><a href="http://syframework.alwaysdata.net">SyBox</a> (PHP playground)</li>
      <li><a href="http://www.tagspaces.org/">TagSpaces</a> (personal data manager)</li>
      <li><a href="https://textbox.io/">Textbox.io</a> (WYSIWYG rich text editor)</a></li>
      <li><a href="https://thefiletree.com">The File Tree</a> (collab editor)</li>
      <li><a href="http://www.mapbox.com/tilemill/">TileMill</a> (map design tool)</li>
      <li><a href="https://tilepieces.net">Tilepieces</a> (visually editing HTML documents and Web applications projects)</li>
      <li><a href="http://doc.tiki.org/Syntax+Highlighter">Tiki</a> (wiki CMS groupware)</li>
      <li><a href="https://www.tistory.com">Tistory</a> (blog service)</li>
      <li><a href="http://www.toolsverse.com/products/data-explorer/">Toolsverse Data Explorer</a> (database management)</li>
      <li><a href="http://blog.englard.net/post/39608000629/codeintumblr">Tumblr code highlighting shim</a></li>
      <li><a href="http://turbopy.com/">TurboPY</a> (web publishing framework)</li>
      <li><a href="http://cruise.eecs.uottawa.ca/umpleonline/">UmpleOnline</a> (model-oriented programming tool)</li>
      <li><a href="https://upsource.jetbrains.com/idea-ce/file/idea-ce-7706e7832aa9e2fd0c2decdb5cbef2225692c696/platform/platform-impl/src/com/intellij/openapi/editor/impl/EditorFactoryImpl.java">Upsource</a> (code browser and review tool)</li>
      <li><a href="https://violentmonkey.github.io/">Violentmonkey</a> (userscript manager / editor)</li>
      <li><a href="https://github.com/mgaitan/waliki">Waliki</a> (wiki engine)</li>
      <li><a href="http://wamer.net/">Wamer</a> (web application builder)</li>
      <li><a href="https://github.com/brettz9/webappfind">webappfind</a> (windows file bindings for webapps)</li>
      <li><a href="http://www.webglacademy.com/">WebGL academy</a> (learning WebGL)</li>
      <li><a href="http://webglplayground.net/">WebGL playground</a></li>
      <li><a href="https://www.webkit.org/blog/2518/state-of-web-inspector/#source-code">WebKit Web inspector</a></li>
      <li><a href="http://www.wescheme.org/">WeScheme</a> (learning tool)</li>
      <li><a href="https://github.com/b3log/wide">Wide</a> (golang web IDE)</li>
      <li><a href="http://wordpress.org/extend/plugins/codemirror-for-codeeditor/">WordPress plugin</a></li>
      <li><a href="http://www.xosystem.org/home/<USER>/xosystem_website/xoside_EN.php">XOSide</a> (online editor)</li>
      <li><a href="http://videlibri.sourceforge.net/cgi-bin/xidelcgi">XQuery tester</a></li>
      <li><a href="http://q42jaap.github.io/xsd2codemirror/">xsd2codemirror</a> (convert XSD to CM XML completion info)</li>
      <li><a href="http://www.yacas.org/yacas_online/yacas_online.html">Yacas Online</a> (interactive mathematical software)</li>
    </ul>

</article>


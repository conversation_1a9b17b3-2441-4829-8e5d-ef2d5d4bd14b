<!doctype html>

<title>CodeMirror: Hard-wrapping Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/markdown/markdown.js"></script>
<script src="../addon/wrap/hardwrap.js"></script>
<style>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Hard-wrapping</a>
  </ul>
</div>

<article>
<h2>Hard-wrapping Demo</h2>
<form><textarea id="code" name="code">Lorem ipsum dolor sit amet, vim augue dictas constituto ex,
sit falli simul viderer te. Graeco scaevola maluisset sit
ut, in idque viris praesent sea. Ea sea eirmod indoctum
repudiare. Vel noluisse suscipit pericula ut. In ius nulla
alienum molestie. Mei essent discere democritum id.

Equidem ponderum expetendis ius in, mea an erroribus
constituto, congue timeam perfecto ad est. Ius ut primis
timeam, per in ullum mediocrem. An case vero labitur pri,
vel dicit laoreet et. An qui prompta conclusionemque, eam
timeam sapientem in, cum dictas epicurei eu.

Usu cu vide dictas deseruisse, eum choro graece adipiscing
ut. Cibo qualisque ius ad, et dicat scripta mea, eam nihil
mentitum aliquando cu. Debet aperiam splendide at quo, ad
paulo nostro commodo duo. Sea adhuc utinam conclusionemque
id, quas doming malorum nec ad. Tollit eruditi vivendum ad
ius, eos soleat ignota ad.
</textarea></form>

<p>Demonstration of
the <a href="../doc/manual.html#addon_hardwrap">hardwrap</a> addon.
The above editor has its change event hooked up to
the <code>wrapParagraphsInRange</code> method, so that the paragraphs
are reflown as you are typing.</p>

<script>
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
  mode: "markdown",
  lineNumbers: true,
  extraKeys: {
    "Ctrl-Q": function(cm) { cm.wrapParagraph(cm.getCursor(), options); }
  }
});
var wait, options = {column: 60}, changing = false;
editor.on("change", function(cm, change) {
  if (changing) return;
  clearTimeout(wait);
  wait = setTimeout(function() {
    changing = true;
    cm.wrapParagraphsInRange(change.from, CodeMirror.changeEnd(change), options);
    changing = false;
  }, 200);
});
</script>

</article>

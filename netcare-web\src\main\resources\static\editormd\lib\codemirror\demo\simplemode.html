<!doctype html>

<title>CodeMirror: Simple Mode Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../addon/mode/simple.js"></script>
<script src="../mode/xml/xml.js"></script>
<style>
  .CodeMirror {border: 1px solid silver; margin-bottom: 1em; }
  dt { text-indent: -2em; padding-left: 2em; margin-top: 1em; }
  dd { margin-left: 1.5em; margin-bottom: 1em; }
  dt {margin-top: 1em;}
</style>

<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Simple Mode</a>
  </ul>
</div>

<article>
<h2>Simple Mode Demo</h2>

<p>The <a href="../addon/mode/simple.js"><code>mode/simple</code></a>
addon allows CodeMirror modes to be specified using a relatively simple
declarative format. This format is not as powerful as writing code
directly against the <a href="../doc/manual.html#modeapi">mode
interface</a>, but is a lot easier to get started with, and
sufficiently expressive for many simple language modes.</p>

<p>This interface is still in flux. It is unlikely to be scrapped or
overhauled completely, so do start writing code against it, but
details might change as it stabilizes, and you might have to tweak
your code when upgrading.</p>

<p>Simple modes (loosely based on
the <a href="https://github.com/mozilla/skywriter/wiki/Common-JavaScript-Syntax-Highlighting-Specification">Common
JavaScript Syntax Highlighting Specification</a>, which never took
off), are state machines, where each state has a number of rules that
match tokens. A rule describes a type of token that may occur in the
current state, and possibly a transition to another state caused by
that token.</p>

<p>The <code>CodeMirror.defineSimpleMode(name, states)</code> method
takes a mode name and an object that describes the mode's states. The
editor below shows an example of such a mode (and is itself
highlighted by the mode shown in it).</p>

<div id="code"></div>

<p>Each state is an array of rules. A rule may have the following properties:</p>

<dl>
  <dt><code><strong>regex</strong>: string | RegExp</code></dt>
  <dd>The regular expression that matches the token. May be a string
  or a regex object. When a regex, the <code>ignoreCase</code> flag
  will be taken into account when matching the token. This regex
  has to capture groups when the <code>token</code> property is
  an array. If it captures groups, it must capture <em>all</em> of the string
  (since JS provides no way to find out where a group matched).
  Currently negative lookbehind assertion for regex is not supported, regardless of browser support.</dd>
  <dt><code><strong>token</strong></code>: string | array&lt;string&gt; | null</dt>
  <dd>An optional token style. Multiple styles can be specified by
  separating them with dots or spaces. When this property holds an array of token styles,
  the <code>regex</code> for this rule must capture a group for each array item.
  </dd>
  <dt><code><strong>sol</strong></code>: boolean</dt>
  <dd>When true, this token will only match at the start of the line.
  (The <code>^</code> regexp marker doesn't work as you'd expect in
  this context because of limitations in JavaScript's RegExp
  API.)</dd>
  <dt><code><strong>next</strong>: string</code></dt>
  <dd>When a <code>next</code> property is present, the mode will
  transfer to the state named by the property when the token is
  encountered.</dd>
  <dt><code><strong>push</strong>: string</code></dt>
  <dd>Like <code>next</code>, but instead replacing the current state
  by the new state, the current state is kept on a stack, and can be
  returned to with the <code>pop</code> directive.</dd>
  <dt><code><strong>pop</strong>: bool</code></dt>
  <dd>When true, and there is another state on the state stack, will
  cause the mode to pop that state off the stack and transition to
  it.</dd>
  <dt><code><strong>mode</strong>: {spec, end, persistent}</code></dt>
  <dd>Can be used to embed another mode inside a mode. When present,
  must hold an object with a <code>spec</code> property that describes
  the embedded mode, and an optional <code>end</code> end property
  that specifies the regexp that will end the extent of the mode. When
  a <code>persistent</code> property is set (and true), the nested
  mode's state will be preserved between occurrences of the mode.</dd>
  <dt><code><strong>indent</strong>: bool</code></dt>
  <dd>When true, this token changes the indentation to be one unit
  more than the current line's indentation.</dd>
  <dt><code><strong>dedent</strong>: bool</code></dt>
  <dd>When true, this token will pop one scope off the indentation
  stack.</dd>
  <dt><code><strong>dedentIfLineStart</strong>: bool</code></dt>
  <dd>If a token has its <code>dedent</code> property set, it will, by
  default, cause lines where it appears at the start to be dedented.
  Set this property to false to prevent that behavior.</dd>
</dl>

<p>The <code>meta</code> property of the states object is special, and
will not be interpreted as a state. Instead, properties set on it will
be set on the mode, which is useful for properties
like <a href="../doc/manual.html#addon_comment"><code>lineComment</code></a>,
which sets the comment style for a mode. The simple mode addon also
recognizes a few such properties:</p>

<dl>
  <dt><code><strong>dontIndentStates</strong>: array&lt;string&gt;</code></dt>
  <dd>An array of states in which the mode's auto-indentation should
  not take effect. Usually used for multi-line comment and string
  states.</dd>
</dl>

<script id="modecode">/* Example definition of a simple mode that understands a subset of
 * JavaScript:
 */

CodeMirror.defineSimpleMode("simplemode", {
  // The start state contains the rules that are initially used
  start: [
    // The regex matches the token, the token property contains the type
    {regex: /"(?:[^\\]|\\.)*?(?:"|$)/, token: "string"},
    // You can match multiple tokens at once. Note that the captured
    // groups must span the whole string in this case
    {regex: /(function)(\s+)([a-z$][\w$]*)/,
     token: ["keyword", null, "variable-2"]},
    // Rules are matched in the order in which they appear, so there is
    // no ambiguity between this one and the one above
    {regex: /(?:function|var|return|if|for|while|else|do|this)\b/,
     token: "keyword"},
    {regex: /true|false|null|undefined/, token: "atom"},
    {regex: /0x[a-f\d]+|[-+]?(?:\.\d+|\d+\.?\d*)(?:e[-+]?\d+)?/i,
     token: "number"},
    {regex: /\/\/.*/, token: "comment"},
    {regex: /\/(?:[^\\]|\\.)*?\//, token: "variable-3"},
    // A next property will cause the mode to move to a different state
    {regex: /\/\*/, token: "comment", next: "comment"},
    {regex: /[-+\/*=<>!]+/, token: "operator"},
    // indent and dedent properties guide autoindentation
    {regex: /[\{\[\(]/, indent: true},
    {regex: /[\}\]\)]/, dedent: true},
    {regex: /[a-z$][\w$]*/, token: "variable"},
    // You can embed other modes with the mode property. This rule
    // causes all code between << and >> to be highlighted with the XML
    // mode.
    {regex: /<</, token: "meta", mode: {spec: "xml", end: />>/}}
  ],
  // The multi-line comment state.
  comment: [
    {regex: /.*?\*\//, token: "comment", next: "start"},
    {regex: /.*/, token: "comment"}
  ],
  // The meta property contains global information about the mode. It
  // can contain properties like lineComment, which are supported by
  // all modes, and also directives like dontIndentStates, which are
  // specific to simple modes.
  meta: {
    dontIndentStates: ["comment"],
    lineComment: "//"
  }
});
</script>

<script>
var sc = document.getElementById("modecode");
var code = document.getElementById("code");
var editor = CodeMirror(code, {
  value: (sc.textContent || sc.innerText || sc.innerHTML),
  mode: "simplemode"
});
</script>

</article>

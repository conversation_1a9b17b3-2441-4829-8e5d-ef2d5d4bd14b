<!doctype html>

<title>CodeMirror: Brainfuck mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="./brainfuck.js"></script>
<style>
	.CodeMirror { border: 2px inset #dee; }
    </style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#"></a>
  </ul>
</div>

<article>
<h2>Brainfuck mode</h2>
<form><textarea id="code" name="code">
[ This program prints "Hello World!" and a newline to the screen, its
  length is 106 active command characters [it is not the shortest.]

  This loop is a "comment loop", it's a simple way of adding a comment
  to a BF program such that you don't have to worry about any command
  characters. Any ".", ",", "+", "-", "&lt;" and "&gt;" characters are simply
  ignored, the "[" and "]" characters just have to be balanced.
]
+++++ +++               Set Cell #0 to 8
[
    &gt;++++               Add 4 to Cell #1; this will always set Cell #1 to 4
    [                   as the cell will be cleared by the loop
        &gt;++             Add 2 to Cell #2
        &gt;+++            Add 3 to Cell #3
        &gt;+++            Add 3 to Cell #4
        &gt;+              Add 1 to Cell #5
        &lt;&lt;&lt;&lt;-           Decrement the loop counter in Cell #1
    ]                   Loop till Cell #1 is zero; number of iterations is 4
    &gt;+                  Add 1 to Cell #2
    &gt;+                  Add 1 to Cell #3
    &gt;-                  Subtract 1 from Cell #4
    &gt;&gt;+                 Add 1 to Cell #6
    [&lt;]                 Move back to the first zero cell you find; this will
                        be Cell #1 which was cleared by the previous loop
    &lt;-                  Decrement the loop Counter in Cell #0
]                       Loop till Cell #0 is zero; number of iterations is 8

The result of this is:
Cell No :   0   1   2   3   4   5   6
Contents:   0   0  72 104  88  32   8
Pointer :   ^

&gt;&gt;.                     Cell #2 has value 72 which is 'H'
&gt;---.                   Subtract 3 from Cell #3 to get 101 which is 'e'
+++++++..+++.           Likewise for 'llo' from Cell #3
&gt;&gt;.                     Cell #5 is 32 for the space
&lt;-.                     Subtract 1 from Cell #4 for 87 to give a 'W'
&lt;.                      Cell #3 was set to 'o' from the end of 'Hello'
+++.------.--------.    Cell #3 for 'rl' and 'd'
&gt;&gt;+.                    Add 1 to Cell #5 gives us an exclamation point
&gt;++.                    And finally a newline from Cell #6
</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "text/x-brainfuck"
      });
    </script>

    <p>A mode for Brainfuck</p>

    <p><strong>MIME types defined:</strong> <code>text/x-brainfuck</code></p>
  </article>

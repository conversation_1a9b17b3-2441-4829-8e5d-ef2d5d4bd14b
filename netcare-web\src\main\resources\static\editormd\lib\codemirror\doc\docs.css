@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: local('Source Sans Pro'), local('SourceSansPro-Regular'), url(source_sans.woff) format('woff');
}

body, html { margin: 0; padding: 0; height: 100%; }
section, article { display: block; padding: 0; }

body {
  background: #f8f8f8;
  font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif;
  line-height: 1.5;
}

p { margin-top: 0; }

h2, h3, h1 {
  font-weight: normal;
  margin-bottom: .7em;
}
h1 { font-size: 140%; }
h2 { font-size: 120%; }
h3 { font-size: 110%; }
article > h2:first-child, section:first-child > h2 { margin-top: 0; }

#nav h1 {
  margin-right: 12px;
  margin-top: 0;
  margin-bottom: 2px;
  color: #d30707;
  letter-spacing: .5px;
}

a, a:visited, a:link, .quasilink {
  color: #A21313;
}

em {
  padding-right: 2px;
}

.quasilink {
  cursor: pointer;
}

article {
  max-width: 700px;
  margin: 0 0 0 160px;
  border-left: 2px solid #E30808;
  border-right: 1px solid #ddd;
  padding: 30px 50px 100px 50px;
  background: white;
  z-index: 2;
  position: relative;
  min-height: 100%;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
}

#nav {
  position: fixed;
  padding-top: 30px;
  max-height: 100%;
  box-sizing: -moz-border-box;
  box-sizing: border-box;
  overflow-y: auto;
  left: 0; right: none;
  width: 160px;
  text-align: right;
  z-index: 1;
}

@media screen and (min-width: 1000px) {
  article {
    margin: 0 auto;
  }
  #nav {
    right: 50%;
    width: auto;
    border-right: 349px solid transparent;
  }
}

#nav ul {
  display: block;
  margin: 0; padding: 0;
  margin-bottom: 32px;
}

#nav a {
  text-decoration: none;
}

#nav li {
  display: block;
  margin-bottom: 4px;
}

#nav li ul {
  font-size: 80%;
  margin-bottom: 0;
  display: none;
}

#nav li.active ul {
  display: block;
}

#nav li li a {
  padding-right: 20px;
  display: inline-block;
}

#nav ul a {
  color: black;
  padding: 0 7px 1px 11px;
}

#nav ul a.active, #nav ul a:hover {
  border-bottom: 1px solid #E30808;
  margin-bottom: -1px;
  color: #E30808;
}

#logo {
  border: 0;
  margin-right: 12px;
  margin-bottom: 25px;
}

section {
  border-top: 1px solid #E30808;
  margin: 1.5em 0;
}

section.first {
  border: none;
  margin-top: 0;
}

#demo {
  position: relative;
}

#demolist {
  position: absolute;
  right: 5px;
  top: 5px;
  z-index: 25;
}

.yinyang {
  position: absolute;
  top: -10px;
  left: 0; right: 0;
  margin: auto;
  display: block;
  height: 120px;
}

.actions {
  margin: 1em 0 0;
  min-height: 100px;
  position: relative;
}

@media screen and (max-width: 800px) {
  .actions {
    padding-top: 120px;
  }
  .actionsleft, .actionsright {
    float: none;
    text-align: left;
    margin-bottom: 1em;
  }
}

th {
  text-decoration: underline;
  font-weight: normal;
  text-align: left;
}

#features ul {
  list-style: none;
  margin: 0 0 1em;
  padding: 0 0 0 1.2em;
}

#features li:before {
  content: "-";
  width: 1em;
  display: inline-block;
  padding: 0;
  margin: 0;
  margin-left: -1em;
}

.rel {
  margin-bottom: 0;
}
.rel-note {
  margin-top: 0;
  color: #555;
}

pre {
  padding-left: 15px;
  border-left: 2px solid #ddd;
}

code {
  padding: 0 2px;
}

strong {
  text-decoration: underline;
  font-weight: normal;
}

.field {
  border: 1px solid #A21313;
}

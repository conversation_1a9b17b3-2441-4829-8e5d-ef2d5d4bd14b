<!doctype html>

<title>CodeMirror: Django template mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<link rel="stylesheet" href="../../theme/mdn-like.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/mode/overlay.js"></script>
<script src="../xml/xml.js"></script>
<script src="../htmlmixed/htmlmixed.js"></script>
<script src="django.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Django</a>
  </ul>
</div>

<article>
<h2>Django template mode</h2>
<form><textarea id="code" name="code">
<!doctype html>
<html>
  <head>
    <title>My Django web application</title>
  </head>
  <body>
    <h1>
      {{ page.title|capfirst }}
    </h1>
    <ul class="my-list">
      {# traverse a list of items and produce links to their views. #}
      {% for item in items %}
      <li>
        <a href="{% url 'item_view' item.name|slugify %}">
          {{ item.name }}
        </a>
      </li>
      {% empty %}
      <li>You have no items in your list.</li>
      {% endfor %}
    </ul>
    {% comment "this is a forgotten footer" %}
    <footer></footer>
    {% endcomment %}
  </body>
</html>
</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        mode: "django",
        indentUnit: 2,
        indentWithTabs: true,
        theme: "mdn-like"
      });
    </script>

    <p>Mode for HTML with embedded Django template markup.</p>

    <p><strong>MIME types defined:</strong> <code>text/x-django</code></p>
  </article>

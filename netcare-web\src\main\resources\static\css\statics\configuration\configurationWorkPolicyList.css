#conf_workpolocy_btn_add,#conf_workpolocy_btn_edit{
		display: none;
		zoom: 1;
		float: none;
	}
	.fileAddModal{
		width:500px;
	}
	.fileAddModal .form-group{
		width:400px;
	}
	/* * {padding:0; margin:0;} */
	.margin {width:1000px; margin:0 auto;position: relative;box-sizing: border-box;z-index: 9999;}
	.ul:after {content:""; display:block; clear:both;}
	.ul {user-select:none;box-sizing: border-box;}
	.li {width: 41px; float:left;color: #fcfefc;box-sizing: border-box;}
	.checkbox {margin: 0;}
	.subgrid-cell span {display: none;}
	.li .mar, .li .smar {height:41px; background-color:#ccc; position:relative;
		border-right: 1px solid #fcfefc;
		border-bottom: 1px solid #fcfefc;
		box-sizing: border-box;
		text-align: center;line-height: 41px;}
	.li .hasCheck {
		background-color: #74b566;
	}
	.header {
		width: 1025px;
		height: 41px;
		background-color: #f3f3f4;
		position: relative;
		right: 41px;
		box-sizing: border-box;
	}
	.header .headerCont {
		width: 41px;
		height: 41px;
		box-sizing: border-box;
		border-right: 1px solid #fcfefc;
		border-bottom: 1px solid #fcfefc;
		text-align: center;
		line-height: 41px;
		float: left;
		color: #545b69;
	}
	.leftRow {
		box-sizing: border-box;
		width: 41px;
		height: 286px;
		position: absolute;
		left: -41px;
		background-color: #f3f3f4;
	}
	.leftRow div {
		width: 41px;
		height: 41px;
		border-right: 1px solid #fcfefc;
		border-bottom: 1px solid #fcfefc;
		color: #545b69;
		text-align: center;
		line-height: 41px;
	}

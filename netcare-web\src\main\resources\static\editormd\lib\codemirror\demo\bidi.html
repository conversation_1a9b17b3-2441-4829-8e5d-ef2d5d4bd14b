<!doctype html>

<title>CodeMirror: Bi-directional Text Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/xml/xml.js"></script>
<style>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
  fieldset {border: none}
</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Bi-directional Text</a>
  </ul>
</div>

<article>
<h2>Bi-directional Text Demo</h2>
<form><textarea id="code" name="code"><!-- Piece of the CodeMirror manual, 'translated' into Arabic by Google Translate -->
<!-- قطعة من دليل CodeMirror، "ترجم" إلى العربية بواسطة جوجل ترجمة -->

<dl>
  <dt id=option_value><code>value (string or Doc)</code></dt>
  <dd>قيمة البداية المحرر. يمكن أن تكون سلسلة، أو. كائن مستند.</dd>
  <dt id=option_mode><code>mode (string or object)</code></dt>
  <dd>وضع الاستخدام. عندما لا تعطى، وهذا الافتراضي إلى الطريقة الاولى
  التي تم تحميلها. قد يكون من سلسلة، والتي إما أسماء أو ببساطة هو وضع
  MIME نوع المرتبطة اسطة. بدلا من ذلك، قد يكون من كائن يحتوي على
  خيارات التكوين لواسطة، مع <code>name</code> الخاصية التي وضع أسماء
  (على سبيل المثال <code>{name: "javascript", json: true}</code>).
  صفحات التجريبي لكل وضع تحتوي على معلومات حول ما معلمات تكوين وضع
  يدعمها. يمكنك أن تطلب CodeMirror التي تم تعريفها طرق وأنواع MIME
  الكشف على <code>CodeMirror.modes</code>
  و <code>CodeMirror.mimeModes</code> الكائنات. وضع خرائط الأسماء
  الأولى لمنشئات الخاصة بهم، وخرائط لأنواع MIME 2 المواصفات
  واسطة.</dd>
  <dt id=option_theme><code>theme (string)</code></dt>
  <dd>موضوع لنمط المحرر مع. يجب عليك التأكد من الملف CSS تحديد
  المقابلة <code>.cm-s-[name]</code> يتم تحميل أنماط (انظر
  <a href="../theme/"><code>theme</code></a> الدليل في التوزيع).
  الافتراضي هو <code>"default"</code> ، والتي تم تضمينها في
  الألوان <code>codemirror.css</code>. فمن الممكن استخدام فئات متعددة
  في تطبيق السمات مرة واحدة على سبيل المثال <code>"foo bar"</code>
  سيتم تعيين كل من <code>cm-s-foo</code> و <code>cm-s-bar</code>
  الطبقات إلى المحرر.</dd>
</dl>
</textarea>
  <fieldset>
    Editor default direction:
    <input type="radio" id="ltr" name="direction"/><label for="ltr">LTR</label>
    <input type="radio" id="rtl" name="direction"/><label for="rtl">RTL</label>
  </fieldset>
  <fieldset>
    HTML document direction:
    <input type="radio" id="htmlltr" name="htmldirection"/><label for="htmlltr">LTR</label>
    <input type="radio" id="htmlrtl" name="htmldirection"/><label for="htmlrtl">RTL</label>
  </fieldset>
  <fieldset>
    <input type="checkbox" id="rtlMoveVisually"/><label for="rtlMoveVisually">Use visual order for arrow key movement.</label>
  </fieldset>
</form>

    <script>
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
  mode: "text/html",
  lineNumbers: true,
  lineWrapping: true,
  direction: "rtl"
});

var dirRadios = {ltr: document.getElementById("ltr"),
                 rtl: document.getElementById("rtl")};
dirRadios[editor.getOption("direction")].checked = true;
dirRadios["rtl"].onchange = dirRadios["ltr"].onchange = function() {
  editor.setOption("direction", dirRadios["rtl"].checked ? "rtl" : "ltr");
};

var HtmlDirRadios = {ltr: document.getElementById("htmlltr"),
                 rtl: document.getElementById("htmlrtl")};
HtmlDirRadios["ltr"].checked = true;
HtmlDirRadios["rtl"].onchange = HtmlDirRadios["ltr"].onchange = function() {
  document.dir = (HtmlDirRadios["rtl"].checked ? "rtl" : "ltr");
};

var moveCheckbox = document.getElementById("rtlMoveVisually");
moveCheckbox.checked = editor.getOption("rtlMoveVisually");
moveCheckbox.onchange = function() {
  editor.setOption("rtlMoveVisually", moveCheckbox.checked);
};
</script>

  <p>Demonstration of bi-directional text support. See
  the <a href="http://marijnhaverbeke.nl/blog/cursor-in-bidi-text.html">related
  blog post</a> for more background.</p>

</article>

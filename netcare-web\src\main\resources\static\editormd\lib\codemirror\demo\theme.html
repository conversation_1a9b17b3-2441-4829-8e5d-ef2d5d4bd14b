<!doctype html>

<title>CodeMirror: Theme Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<link rel="stylesheet" href="../theme/3024-day.css">
<link rel="stylesheet" href="../theme/3024-night.css">
<link rel="stylesheet" href="../theme/abbott.css">
<link rel="stylesheet" href="../theme/abcdef.css">
<link rel="stylesheet" href="../theme/ambiance.css">
<link rel="stylesheet" href="../theme/ayu-dark.css">
<link rel="stylesheet" href="../theme/ayu-mirage.css">
<link rel="stylesheet" href="../theme/base16-dark.css">
<link rel="stylesheet" href="../theme/bespin.css">
<link rel="stylesheet" href="../theme/base16-light.css">
<link rel="stylesheet" href="../theme/blackboard.css">
<link rel="stylesheet" href="../theme/cobalt.css">
<link rel="stylesheet" href="../theme/colorforth.css">
<link rel="stylesheet" href="../theme/dracula.css">
<link rel="stylesheet" href="../theme/duotone-dark.css">
<link rel="stylesheet" href="../theme/duotone-light.css">
<link rel="stylesheet" href="../theme/eclipse.css">
<link rel="stylesheet" href="../theme/elegant.css">
<link rel="stylesheet" href="../theme/erlang-dark.css">
<link rel="stylesheet" href="../theme/gruvbox-dark.css">
<link rel="stylesheet" href="../theme/hopscotch.css">
<link rel="stylesheet" href="../theme/icecoder.css">
<link rel="stylesheet" href="../theme/isotope.css">
<link rel="stylesheet" href="../theme/juejin.css">
<link rel="stylesheet" href="../theme/lesser-dark.css">
<link rel="stylesheet" href="../theme/liquibyte.css">
<link rel="stylesheet" href="../theme/lucario.css">
<link rel="stylesheet" href="../theme/material.css">
<link rel="stylesheet" href="../theme/material-darker.css">
<link rel="stylesheet" href="../theme/material-palenight.css">
<link rel="stylesheet" href="../theme/material-ocean.css">
<link rel="stylesheet" href="../theme/mbo.css">
<link rel="stylesheet" href="../theme/mdn-like.css">
<link rel="stylesheet" href="../theme/midnight.css">
<link rel="stylesheet" href="../theme/monokai.css">
<link rel="stylesheet" href="../theme/moxer.css">
<link rel="stylesheet" href="../theme/neat.css">
<link rel="stylesheet" href="../theme/neo.css">
<link rel="stylesheet" href="../theme/night.css">
<link rel="stylesheet" href="../theme/nord.css">
<link rel="stylesheet" href="../theme/oceanic-next.css">
<link rel="stylesheet" href="../theme/panda-syntax.css">
<link rel="stylesheet" href="../theme/paraiso-dark.css">
<link rel="stylesheet" href="../theme/paraiso-light.css">
<link rel="stylesheet" href="../theme/pastel-on-dark.css">
<link rel="stylesheet" href="../theme/railscasts.css">
<link rel="stylesheet" href="../theme/rubyblue.css">
<link rel="stylesheet" href="../theme/seti.css">
<link rel="stylesheet" href="../theme/shadowfox.css">
<link rel="stylesheet" href="../theme/solarized.css">
<link rel="stylesheet" href="../theme/the-matrix.css">
<link rel="stylesheet" href="../theme/tomorrow-night-bright.css">
<link rel="stylesheet" href="../theme/tomorrow-night-eighties.css">
<link rel="stylesheet" href="../theme/ttcn.css">
<link rel="stylesheet" href="../theme/twilight.css">
<link rel="stylesheet" href="../theme/vibrant-ink.css">
<link rel="stylesheet" href="../theme/xq-dark.css">
<link rel="stylesheet" href="../theme/xq-light.css">
<link rel="stylesheet" href="../theme/yeti.css">
<link rel="stylesheet" href="../theme/idea.css">
<link rel="stylesheet" href="../theme/darcula.css">
<link rel="stylesheet" href="../theme/yonce.css">
<link rel="stylesheet" href="../theme/zenburn.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/javascript/javascript.js"></script>
<script src="../addon/selection/active-line.js"></script>
<script src="../addon/edit/matchbrackets.js"></script>
<style>
      .CodeMirror {border: 1px solid black; font-size:13px}
    </style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Theme</a>
  </ul>
</div>

<article>
<h2>Theme Demo</h2>
<form><textarea id="code" name="code">
function findSequence(goal) {
  function find(start, history) {
    if (start == goal)
      return history;
    else if (start > goal)
      return null;
    else
      return find(start + 5, "(" + history + " + 5)") ||
             find(start * 3, "(" + history + " * 3)");
  }
  return find(1, "1");
}</textarea></form>

<p>Select a theme: <select onchange="selectTheme()" id=select>
    <option selected>default</option>
    <option>3024-day</option>
    <option>3024-night</option>
    <option>abbott</option>
    <option>abcdef</option>
    <option>ambiance</option>
    <option>ayu-dark</option>
    <option>ayu-mirage</option>
    <option>base16-dark</option>
    <option>base16-light</option>
    <option>bespin</option>
    <option>blackboard</option>
    <option>cobalt</option>
    <option>colorforth</option>
    <option>darcula</option>
    <option>dracula</option>
    <option>duotone-dark</option>
    <option>duotone-light</option>
    <option>eclipse</option>
    <option>elegant</option>
    <option>erlang-dark</option>
    <option>gruvbox-dark</option>
    <option>hopscotch</option>
    <option>icecoder</option>
    <option>idea</option>
    <option>isotope</option>
    <option>juejin</option>
    <option>lesser-dark</option>
    <option>liquibyte</option>
    <option>lucario</option>
    <option>material</option>
    <option>material-darker</option>
    <option>material-palenight</option>
    <option>material-ocean</option>
    <option>mbo</option>
    <option>mdn-like</option>
    <option>midnight</option>
    <option>monokai</option>
    <option>moxer</option>
    <option>neat</option>
    <option>neo</option>
    <option>night</option>
    <option>nord</option>
    <option>oceanic-next</option>
    <option>panda-syntax</option>
    <option>paraiso-dark</option>
    <option>paraiso-light</option>
    <option>pastel-on-dark</option>
    <option>railscasts</option>
    <option>rubyblue</option>
    <option>seti</option>
    <option>shadowfox</option>
    <option>solarized dark</option>
    <option>solarized light</option>
    <option>the-matrix</option>
    <option>tomorrow-night-bright</option>
    <option>tomorrow-night-eighties</option>
    <option>ttcn</option>
    <option>twilight</option>
    <option>vibrant-ink</option>
    <option>xq-dark</option>
    <option>xq-light</option>
    <option>yeti</option>
    <option>yonce</option>
    <option>zenburn</option>
</select>
</p>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
    lineNumbers: true,
    styleActiveLine: true,
    matchBrackets: true
  });
  var input = document.getElementById("select");
  function selectTheme() {
    var theme = input.options[input.selectedIndex].textContent;
    editor.setOption("theme", theme);
    location.hash = "#" + theme;
  }
  var choice = (location.hash && location.hash.slice(1)) ||
               (document.location.search &&
                decodeURIComponent(document.location.search.slice(1)));
  if (choice) {
    input.value = choice;
    editor.setOption("theme", choice);
  }
  CodeMirror.on(window, "hashchange", function() {
    var theme = location.hash.slice(1);
    if (theme) { input.value = theme; selectTheme(); }
  });
</script>
</article>

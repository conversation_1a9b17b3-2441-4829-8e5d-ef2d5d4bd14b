<!doctype html>

<title>CodeMirror: Autoresize Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../mode/css/css.js"></script>
<style>
      .CodeMirror {
        border: 1px solid #eee;
        height: auto;
      }
    </style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Autoresize</a>
  </ul>
</div>

<article>
<h2>Autoresize Demo</h2>
<form><textarea id="code" name="code">
.CodeMirror {
  border: 1px solid #eee;
  height: auto;
}
</textarea></form>

<p>By setting an editor's <code>height</code> style
to <code>auto</code> and giving
the <a href="../doc/manual.html#option_viewportMargin"><code>viewportMargin</code></a>
a value of <code>Infinity</code>, CodeMirror can be made to
automatically resize to fit its content.</p>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        viewportMargin: Infinity
      });
    </script>

  </article>

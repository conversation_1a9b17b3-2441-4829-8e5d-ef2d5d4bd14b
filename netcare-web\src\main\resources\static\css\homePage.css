/*::-webkit-scrollbar    //滚动条整体部分
::-webkit-scrollbar-button   //滚动条两端的按钮
::-webkit-scrollbar-track   // 外层轨道
::-webkit-scrollbar-track-piece    //内层轨道，滚动条中间部分（除去）
::-webkit-scrollbar-thumb //滚动条里面可以拖动的那个
::-webkit-scrollbar-corner   //边角
::-webkit-resizer   ///定义右下角拖动块的样式*/
::-webkit-scrollbar-track {
	background-color: #3c4b58;
}

.contact-box {
	width: 100%;
	height: 780px;
	background: #f3f3f4;
	border: none;
}

.totalInfo {
	width: 100%;
	height: 110px;
	background: #f3f3f4;
	border-bottom: 5px solid #e7eaec;
}

.logo {
	width: 22%;
	height: 90px;
	background: #fff;
	float: left;
	background: url(../img/logoBg1.jpg) no-repeat;
	background-size: 100% 100%;
}

.netCount {
	width: 22%;
	height: 90px;
	background: #fff;
	float: left;
	margin-left: 20px;
}

.serverMid {
	width: 22%;
	height: 90px;
	background: #fff;
	float: left;
	margin-left: 20px;
}

.server,
.network {
	float: left;
	width: 50%;
	height: 100%;
	text-align: center;
	padding-top: 20px;
}

.netP1,
.serverP1 {
	color: #373737;
}

.netP2,
.serverP2 {
	width: 60px;
	height: 25px;
	margin: 0 auto;
	line-height: 25px;
	color: #fff;
	border-radius: 15px;
}

.netP2 {
	background: #d25e5e;
}

.serverP2 {
	background: #66cc9a;
}

.count {
	border-bottom: 1px solid #ccc;
	width: 100%;
	height: 30px;
	line-height: 30px;
}

.Num {
	width: 100%;
	height: 70px;
	line-height: 60px;
	text-align: center;
	font-size: 36px;
	font-weight: 600;
}

.advite {
	/* width: 800px; */
	width: 90%;
}

.advite .customerPhone {
	min-width: 300px;
	height: 30px;
	color: #434445;
	float: left;
	list-style: none;
	overflow: hidden;
}

.customerPhone li {
	/* width: 300px; */
	width: 80%;
	height: 30px;
	line-height: 30px;
	font-size:14px;
	overflow: hidden;
}

.yearT {
	background: #23c6c8;
	color: #FFF;
	padding: 3px 5px;
	border-radius: 4px;
}

.infor {
	width: 28%;
	height: 124px;
	background: #ebebed;
	float: left;
	margin-left: 25px;
	margin-top: -20px;
}

.inforTop,
.inforBot {
	width: 100%;
	height: 50%;
}

.inforBot {
	padding: 31px 0 0 8px;
}

.inforTop {
	padding: 10px 0 0 8px;
}


/*柱状图*/
#cont {
	width: 100%;
	height: 300px;
	background: #fff;
	padding-top: 20px;
	background: #fcfdff;

}

.Critical {
	width: 100%;
	height: 50px;
}

.Ctil {
	width: 100%;
	height: 30px;
}

.prossD {
	width: 100%;
	height: 5px;
	background: #ccc;
}

.prossD div {
	height: 5px;
	background: #cc0000;
}

#recordBar {
	width: 80%;
	height: 100%;
	float: left;
	margin-left: -25px;
}

#recordBar #recordBarC {
	width: 100%;
	height: 100%;
}

#recordBar canvas {
	width: 100% !important;

}

#cont-right {
	width: 20%;
	height: 100%;
	float: left;
	margin-top: 20px;

}

.months {
	background: #0083c2;
	color: #fff;
	padding: 1px 7px;
	border-radius: 4px;
	position: relative;
	left: 16%;
}

.pieCont {
	width: 100%;
	height: 360px;
	margin-top: 20px;
}

.changeSign {
	color: #fff;
}

.deviceNum {
	color: #434445;
	font-size: 15px;
}

.bln {
	display: none;
}

.mapChange {
	width: 100%;
	text-align: center;
	cursor: pointer;
	color: #ff0;
	position: relative;
}

.warnD {
	position: absolute;
	width: 60px;
	top: -14px;
	left: -22px;
	color: #fff;
}

.mapChange i {
	margin-right: 20px;
}

.text-navy {
	color: #d7683d !important;
}

.mapR .table tr:hover {
	color: #000;
}

.mapR .table thead tr:hover {
	color: #fff;
}

.trans {
	width: 100%;
	height: 360px;
}

.Taskcont td,
.Taskcont td span {
	white-space: pre-line;
	overflow: hidden;
}

.Taskcont tr {
	max-height: 36px;
}

#echartsBar4 {
	width: 62%;
	height: 280px;
	float: left;

}

#echartsPie,
#echartsPie1 {
	width: 100%;
	height: 100%;
}

.months1 {
	background: #1c84c6;
	color: #fff;
	padding: 2px 8px;
	border-radius: 5px;
	position: relative;
	top: -264px;
	left: 86%;
}

/*散点图*/
.setPlace {
	width: 100%;
	height: 451px;
}
.ibox{
	background: #fcfdff;
}
.ibox-content {
	background: #fcfdff;
	border: none;
}

.ibox-title {
	background: #fcfdff;
	border: none;
	border-bottom: 1px solid #ccc;
	color: #434445;
	min-height: 36px !important;
}

.ibox-tools {
	color: #e36837;
}

.stL {
	width: 64%;
	height: 330px;
	background: #fcfdff;
	float: left;
}

.stR {
	width: 35%;
	height: 451px;
	background: #fcfdff;
	margin-left: 1%;
	float: left;
}

.stR-01,
.stR-02,
.stR-03,
.stR-04,
.stR-05,
.stR-06 {
	width: 45%;
	background: #fcfdff;
	float: left;
	height: 138px;
}

.stR-02,
.stR-04,
.stR-06 {
	margin-left: 3%;
}

.stR-03,
.stR-04,
.stR-05,
.stR-06 {
	margin-top: 11px;
}

.netN {
	font-weight: 500;
	text-align: center;
	margin-top: 30px;
	font-size: 20px;
	color: #17233d;
}

.imgSP {
	position: relative;
	top: -5px;
}

.sysC {
	text-align: center;
	cursor: pointer;
}

.nSpan {
	font-size: 27px;
	line-height: 36px;
}

.stR .item {
	width: 100%;
	height: 315px;
}

.netT,
.netB {
	height: 100%;
	width: 50%;
	float: left;
}

.netT .ne {
	margin-top: 26px;
	width: 95%;
	height: 24px;
	background: #40a42b;
	text-align: center;
	line-height: 24px;
	border-radius: 10px;
	cursor: pointer;
}

.netT .se {
	margin-top: 15px;
	width: 95%;
	height: 24px;
	background: #e36837;
	text-align: center;
	line-height: 24px;
	border-radius: 10px;
	cursor: pointer;
}

.mapL {
	width: 79%;
	height: 365px;
	float: left;
}

.mapR {
	width: 21%;
	height: 320px;
	float: left;
}

#echartsMap {
	width: 100%;
	height: 100%;
}

#echartsMapW {
	width: 100%;
	height: 100%;
}

.topC {
	width: 100%;
	height: 250px;
}

#noData,
#LatencyTopCont,
#memoryTopCont,
#cpuTopCont,
#InUtilizationTopCont,
#PacketLossTopCont,
#ReachabilityTopCont,
#outUtilizationTopCont {
	width: 100%;
	height: 250px;
	text-align: center;
	line-height: 250px;
	font-size: 28px;
}

.Broadcast {
	position: relative;
	bottom: 10px;
	width: 100%;
	visibility: hidden;
}

/*去掉百度地图左下角标志*/
.anchorBL {
	display: none !important;
}

.mapR .table thead tr:hover {
	background-color: #f5f5f5;
	color: #000;
}
/* 首页-2 */
.deviceAll{
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #9b9c9d;
    position: relative;
    height: 65px;
}
.deviceAll .dev—item,.dev—item1{
    width: 13%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: #fff;
    border-radius: 10px;
}
.deviceAll .dev-name{
    margin-bottom: 10px;
}
.deviceAll .dev-num{
    display: flex;
    align-items: center;
}
.deviceAll .dev-num .dian{
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #92d77b;
    margin-right: 8px;
}
.deviceAll .dev-num .hue{
    color: #5cd0c8;
    margin: 0 5px;
}
.chart{
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin: 30px 0;
}
.chart-item{
    width: 49%;
    background: #fff;
    border-radius: 10px;
    padding: 20px 15px;
}
.item-desc{
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}
.item-desc .item-tle{
    font-size: 16px;
    color: #696969;
}
.item-day{
    border: 1px solid #e2e2e2;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    width: 180px;
}
.item-day span{
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    height: 34px;
    line-height: 34px;
    width: 100%;
    text-align: center;
}
.item-day span:nth-child(2){
    border-left: 1px solid #e2e2e2;
    border-right: 1px solid #e2e2e2;
}
.item-day span:nth-child(1){
    background: #3e97ff;
    color: #fff;
}
#flowTrend,#cmdTrend{
    width: 100%;
    height: 380px;
}
.form-select{
    width: 150px;
    position: absolute;
    right: 190px;
}
.loca-con{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 16px;
    color: #696969;
}
.loca-con .search{
    display: flex;
    align-items: center;
}
.loca-con .search span{
    display: inline-block;
    width: 60px;
    cursor: pointer;
    padding: 0 5px;
    background: #3e97ff;
    height: 34px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    text-align: center;
    line-height: 34px;
    font-size: 14px;
    color: #fff;
}
.switchover{
    display: flex;
    justify-content: flex-end;
    position: relative;
}
.switchover i{
    cursor: pointer;
    position: absolute;    
    top: -16px;
    right: -14px;
}

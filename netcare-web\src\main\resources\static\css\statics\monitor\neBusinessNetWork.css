.clearfix:after,.clearfix:before { content: ''; display: block; height: 0; visibility:hidden;clear: both; }
.clearfix { zoom: 1; }
.clear { clear: both; }

.form-horizontal .form-group{
    float: left;
    width:260px;
}
/*.form-group>div{
    white-space: nowrap;
}*/
.toolbar>div {
    display: inline-block;
    font-size:14px;
    padding: 8px 5px;
    border-right: 1px solid #ddd;
    cursor: pointer;
}
.toolbar,.deviceTot {
    border: 1px solid #ddd;
    border-bottom: 0;
    background-color: #F9F9F9;
}
.jqGrid_wrapper{
    background-color: #fcfdff;
}
.deviceTot{
    padding: 8px;
}
.blk{
	display: block;
}
.bln{
	display: none;
}
.warnC p {
	line-height: 32px;
}
.modal-footer {
	text-align: center;
}
.deiverName,.marK {
	color: #f00;
	font-weight: 600;
	font-size: 20px;
	cursor: pointer;
}

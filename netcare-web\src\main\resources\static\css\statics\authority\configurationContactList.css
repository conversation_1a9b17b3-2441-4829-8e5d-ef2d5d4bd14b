.toolbar>div {
    display: inline-block;
    font-size:14px;
    padding: 8px 5px;
    border-right: 1px solid #ddd;
    cursor: pointer;
}
.radio{
    display: inline-block;
}
.radio label{
    padding-left: 0;
    padding-right: 20px;
}
.modal .phoneTableAdd{
    width:300px;
    margin: 50px auto;
}
.form-search{
    height: 25px;
}
.form-horizontal{
    margin-bottom: 20px;
}
.phoneTable div,.emailTable div,.smsTable div{
    height: 36px;
    line-height: 36px;
    border: 1px solid #ddd;
    margin-left: -2px;
}
.phoneTable .phoneTop,.emailTable .emailTop,.smsTable .smsTop{
    background-color: #f5f5f5;
}
.phoneTable .phoneTitle,.emailTable .emailTitle,.smsTable .smsTitle {
    font-size: 14px;
    background: #fcfcfc;
}
.active{
    background-color: #ecf5ff;
}
.phoneTable .phoneAct,.emailTable .emailAct,.smsTable .smsAct{
    padding-left: 12px;
    cursor: pointer;
}
/*.isInternationaleShow,.isSmsReShow,.isEmaShow{
    display: none;
}*/
.policyerrorTip{
    display: none;
    color: red;
    text-align: center;
    height: 20px;
    line-height: 40px;
}
.policyIsDel,.policyErrorTip{
    display: none;
}

<!doctype html>

<title>CodeMirror: HTML5 preview</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel=stylesheet href=../lib/codemirror.css>
<script src=../lib/codemirror.js></script>
<script src=../mode/xml/xml.js></script>
<script src=../mode/javascript/javascript.js></script>
<script src=../mode/css/css.js></script>
<script src=../mode/htmlmixed/htmlmixed.js></script>
<style type=text/css>
      .CodeMirror {
        float: left;
        width: 50%;
        border: 1px solid black;
      }
      iframe {
        width: 49%;
        float: left;
        height: 300px;
        border: 1px solid black;
        border-left: 0px;
      }
    </style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">HTML5 preview</a>
  </ul>
</div>

<article>
<h2>HTML5 preview</h2>

    <textarea id=code name=code>
<!doctype html>
<html>
  <head>
    <meta charset=utf-8>
    <title>HTML5 canvas demo</title>
    <style>p {font-family: monospace;}</style>
  </head>
  <body>
    <p>Canvas pane goes here:</p>
    <canvas id=pane width=300 height=200></canvas>
    <script>
      var canvas = document.getElementById('pane');
      var context = canvas.getContext('2d');

      context.fillStyle = 'rgb(250,0,0)';
      context.fillRect(10, 10, 55, 50);

      context.fillStyle = 'rgba(0, 0, 250, 0.5)';
      context.fillRect(30, 30, 55, 50);
    </script>
  </body>
</html></textarea>
    <iframe id=preview></iframe>
    <script>
      var delay;
      // Initialize CodeMirror editor with a nice html5 canvas demo.
      var editor = CodeMirror.fromTextArea(document.getElementById('code'), {
        mode: 'text/html'
      });
      editor.on("change", function() {
        clearTimeout(delay);
        delay = setTimeout(updatePreview, 300);
      });
      
      function updatePreview() {
        var previewFrame = document.getElementById('preview');
        var preview =  previewFrame.contentDocument ||  previewFrame.contentWindow.document;
        preview.open();
        preview.write(editor.getValue());
        preview.close();
      }
      setTimeout(updatePreview, 300);
    </script>
  </article>

.researchDiagnose{
    display: none;
}
#stepNe{
    color: red;
    font-weight: 700;
}
#hangModal #solutionHangTime{
    font-size: 15px !important;
}
.deviceWid .form-group{
    width:260px ;
    margin: 5px 0 0 -20px;
}
#fillTipForSolve{
    color: red;
    font-weight: 700;
}
#DiagnoseTab td {
    /*padding: 0 10px;*/
}
.f_l{
    float: left;
}
#DiagnoseTab td>a{
    display:none;
}
.workmeth{
    margin-top: 10px;
}
#time .form-control{
    margin-top: 10px !important;
}


.workmeth input.form-control{
    height: 26px !important;
}
#knowledgeP .form-group{
    margin-left: 10px !important;
}
.pignose-tab-wrapper .pignose-tab-container{
    top:36px;
    padding: 0 !important;
}
.nav-tabs>li>a{
    padding: 8px 5px !important;
    font-weight: 400 !important;
}
#deviceAdd .modal-body {
    padding: 0 28px;
}
#sousuoLabel .form-control {
    height: 26px;
}
#sousuoLabel{
    margin-bottom: 10px !important;
}
.pignose-tab-wrapper .pignose-tab-group{
    margin-top: 10px !important;
}
.Step .Tab_shinc{
    height: 43px;
}
.Step .Tab_shinc li{
    float: left;
    font-size:14px;
    color: #91a6b8;
    line-height: 85px;
    list-style: none;
}
#menuControlTab td {
    white-space: nowrap;
}
.table_inc td {
    white-space: nowrap;
}
.Step .Tab_shinc li{
    float: left;
    white-space: nowrap;
    color: #A3A3A3;
    font-weight: 700;
    line-height: 64px;
}

.T_1,.T_2,.T_3,.T_4,.T_5{
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #A3A3A3;
    margin-top: 6px;
    display: block;
    /*padding-left:10px ;*/
}
.incdtNewLine,.incdtthreeLine,.incdtfourLine,.incdtfiveLine{
    width: 20%;
    height: 1px;
    border: 1px solid #A3A3A3;
    margin-top: 12px;
    opacity: 0.9;
}

#problemNew span,#problemthree span, #problemfour span, #problemfive span, #problemSix span,#DiagnoseNew span,#Diagnosetwo span, #Diagnosethree span, #Diagnosefour span, #Diagnosefive span{
    width: 44px;
    height: 52px;
    display: block;
    color: #3E97FF;
    background: url(../../../img/Tab_icon1.jpg) no-repeat -270px -11px;
    margin-top: -5px;
}
#problemNew span,#DiagnoseNew span{
    background: url(../../../img/Tab_icon1.jpg) no-repeat -32px -11px;
}
#problemNew,#problemthree,#problemfour,#problemfive,#problemSix,#DiagnoseNew,#Diagnosetwo, #Diagnosethree, #Diagnosefour, #Diagnosefive{
    margin-left: -26px;
}
#deviceC .form-horizontal{
    margin-bottom: 0 !important;
}
#solve select{
    height: 26px;
    padding:0;
    margin-left: 8px;
    line-height: 26px;
}
.lineH{
    line-height: 33px !important;
}
#time .form-control{
    height: 30px;
}

.Oper_div {
    background: #3E97FF;
    overflow: hidden;
    clear: both;
    color: #fff;
}
.Oper_div td a:hover {
    color: #fff;
    text-decoration: underline;
}
.Oper_div td a {
    color: #fff;
    display: inline-block;
    height: 28px;
    line-height: 28px;
    padding-left: 20px;
    margin: 0px 3px;
}
.next_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px 0px;
}
.req_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -140px;
}
.get_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -56px;
}
.rep_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -336px;
}
.save_btn{
    background: url(../../../img/Oper_btn.png) no-repeat 0px -80px;
}
.Rollback_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -280px;
}
.Cancel_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -308px;
}
.Mission_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -392px;
}
.hang_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -112px;
}
.Relieve_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -448px;
}
.Accep_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -28px;
}
#menuControlTab a{
    color: #fff;
    display: inline-block;
    padding-left: 20px;
    margin: 0px 3px;
}
.progress-bar-default {
    height: 15px !important;
}
.pignose-tab-wrapper .pignose-tab-btn{
    min-width:0;
    padding: 0 0.5em;
}
.progress{
    margin-bottom: 0 !important;
}
.progress-bar {
    background-color: #c0efb8;
}
.inc_hr {
    border-top: 1px dashed #c7c7c7;
    width: 95%;
    height: 5px;
    margin: 10px auto;
}
.table_inc td {
    line-height: 26px;
    color: #09121a;
}
.table_inc td, .table_inc th {
    padding: 0px 5px;
    text-align: left;
}
.font_red {
    color: #fe0e0e;
    font-size:14px;
    font-weight: 700;
}
#companyCombByHandle, #helpBtn {
    float: left;
}
#helpBtn{
    height: 24px;
    line-height: 20px;
}
input.inc_input {
    width: 142px;
    border: 1px solid #b3b6b9;
    height: 20px;
}
.table_inc .form-control{
    height: 25px;
    padding: 0;
}
input#occurTime {
    font-size: 9px;
    padding-left: 0;
}
.ibox-content{
    padding: 0;
}
.pignose-tab-wrapper .pignose-tab-btn.active {
    color: #fff;
    background-color: #3E97FF;
}
.pignose-subTab-btn.active {
    color: #555 !important;
    background-color: #fff !important;
}
#fileAddLabel .form-group{
    width:538px;
}

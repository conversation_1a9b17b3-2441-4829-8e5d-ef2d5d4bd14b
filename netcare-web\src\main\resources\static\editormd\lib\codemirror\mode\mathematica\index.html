<!doctype html>

<title>CodeMirror: Mathematica mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel=stylesheet href=../../lib/codemirror.css>
<script src=../../lib/codemirror.js></script>
<script src=../../addon/edit/matchbrackets.js></script>
<script src=mathematica.js></script>
<style type=text/css>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Mathematica</a>
  </ul>
</div>

<article>
<h2>Mathematica mode</h2>


<textarea id="mathematicaCode">
(* example Mathematica code *)
(* Dualisiert wird anhand einer Polarität an einer
   Quadrik $x^t Q x = 0$ mit regulärer Matrix $Q$ (also
   mit $det(Q) \neq 0$), z.B. die Identitätsmatrix.
   $p$ ist eine Liste von Polynomen - ein Ideal. *)
dualize::"singular" = "Q must be regular: found Det[Q]==0.";
dualize[ Q_, p_ ] := Block[
    { m, n, xv, lv, uv, vars, polys, dual },
    If[Det[Q] == 0,
      Message[dualize::"singular"],
      m = Length[p];
      n = Length[Q] - 1;
      xv = Table[Subscript[x, i], {i, 0, n}];
      lv = Table[Subscript[l, i], {i, 1, m}];
      uv = Table[Subscript[u, i], {i, 0, n}];
      (* Konstruiere Ideal polys. *)
      If[m == 0,
        polys = Q.uv,
        polys = Join[p, Q.uv - Transpose[Outer[D, p, xv]].lv]
        ];
      (* Eliminiere die ersten n + 1 + m Variablen xv und lv
         aus dem Ideal polys. *)
      vars = Join[xv, lv];
      dual = GroebnerBasis[polys, uv, vars];
      (* Ersetze u mit x im Ergebnis. *)
      ReplaceAll[dual, Rule[u, x]]
      ]
    ]
</textarea>

<script>
  var mathematicaEditor = CodeMirror.fromTextArea(document.getElementById('mathematicaCode'), {
    mode: 'text/x-mathematica',
    lineNumbers: true,
    matchBrackets: true
  });
</script>

<p><strong>MIME types defined:</strong> <code>text/x-mathematica</code> (Mathematica).</p>
</article>

.dashboardTitle{
    background-color: #293846 !important;
    color: #ccc !important;
    padding: 15px 20px 5px !important;
}
#table-head,.dashboardTitle {
    position: relative;
}
#table-head{
    /* top:94px; */
    z-index: 9998;
    height: 1px;
}
.dashboardTitle{
    width: 100%;
    z-index: 9999;
}
.modal-header {
    background-color: #3E97FF;
    color: #fff;
}
.table .btn-primary{
    color: #fff !important;
    font-weight: 700;
}
.btn{
    color: #555 !important;
    line-height:1 !important;
}
.form-control{
    height: 28px !important;
}
.btn-white{
    background-color: none !important;
}
.wrapper-content,.wrapper {
    padding: 0px !important;
}
.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th{
    padding: 2px !important;
}
.clear-padding{
    padding-left: 0px;
    padding-right:0px;
}

.clear-margin{
    margin-left: 0px;
    margin-right: 0px;
}

th .btn-group{
    width: 100%;
}
th .btn-group span{
    padding-left: 0px;
    padding-right:0px;
}
th .btn-group  .glyphicon{
    margin-right: 0px;
}
th  .glyphicon:hover{
    cursor:pointer;
}
th .group-th-click:hover{
    color: red;
    cursor:pointer;
}
.row-height{
    height: 35px;

}
.label-hight{
    line-height: 35px;
}
.checkbox-margin {
   margin-left:15px ;
}
.left-border{
    /*border-left: solid  1px  #F3F3F4;*/
}
.dropdown-menu{
    color: #777;
}
.dropdown-menu ul li {
    list-style-type:none;
}
.th-icon-margin{
    margin-right: 5px;
}
.filter-pop{
    min-width: 200px;
    height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
}
.filter-pop .inverse{
    margin-top: 7px;
}
.filter-pop .sel-all{
    margin-top:12px;
}
.row-hidden{
    display: none;
}


table {
    table-layout:fixed
}

table tr td{
    overflow:hidden;
    white-space:nowrap;
    text-overflow:ellipsis;
    -o-text-overflow:ellipsis;
    -moz-text-overflow: ellipsis;
    -webkit-text-overflow: ellipsis;
}

#default-table{
    margin-top: 134px;
}
#tree-table  .node-text{
    line-height: 25px;
}


.th-drop {
    line-height: 40px;
    border: solid 1px #e7eaec;
    padding: 5px 10px;
    height: 50px;
}
.th-drop .group-item{
    min-width:100px;
    height: 35px;
    line-height: 35px;
    float:left;
    color: #555;
    background-color: #Fff;
    padding: auto 0px;
    margin-left: 5px;
}
.th-drop .group-item span{
    width: 80%;
    border-right: solid 1px  #DDDDDD;
}
.th-drop .group-item i{
    margin-right: 5px;
    width: 15px;
    height: 15px;
}
.serviceIcon>span{
    width: 85px;
    color:#fff;
    font:bold;
    display: inline-block;
}
.serviceIcon>.Normal,.serviceIcon>.Normal_acked,.serviceIcon>.Normal_ed{
    background-color: #5CB85C;
}
.serviceIcon>.Critical,.serviceIcon>.Critical_acked,.serviceIcon>.Critical_ed{
    background-color:#D9534F;
}
.serviceIcon>.Minor,.serviceIcon>.Minor_acked,.serviceIcon>.Minor_ed{
    background-color: #E9D310;
}
.serviceIcon>.Warning,.serviceIcon>.Warning_acked,.serviceIcon>.Warning_ed{
    background-color: #D7B100;
}
.serviceIcon>.Informational,.serviceIcon>.Informational_acked,.serviceIcon>.Informational_ed{
    background-color: #23c6c8;
}
.serviceIcon>.Unknown,.serviceIcon>.Unknown_acked,.serviceIcon>.Unknown_ed{
    background-color: #ccc;
}
.serviceIcon>.Major,.serviceIcon>.Major_acked,.serviceIcon>.Major_ed{
    background-color: #F0AD4E;
}
.serviceIcon>.Monitoring,.serviceIcon>.Monitoring_acked,.serviceIcon>.Monitoring_ed{
  background-color: #6B95EB;
}
.priority_info{
    background-color: #23c6c8;
}
.priority_warn{
    background-color: #D7B100;
}
.priority_error{
    background-color: #D9534F;
}
.priority_critical{
    background-color: #ff0000;
}
.priority_all{
    width: 65px;
    color: #fff;
    font: bold;
    display: inline-block;
    padding: 3px 2px;
    border-radius: 5px 5px 5px 5px;
}
.table{
    /*margin-bottom: 20px !important;*/
}
.Normal,.Normal_acked,.Normal_ed,.Critical,.Critical_acked,.Critical_ed,.Minor,.Minor_acked,.Minor_ed,.Warning,.Warning_acked,.Warning_ed,.Informational,.Informational_acked,.Informational_ed,.Unknown,.Unknown_acked,.Unknown_ed,.Major,.Major_acked,.Major_ed{
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
}
/*.Normal_acked{
    background-color: #5CB85C;
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
    font-size: 10px;
}
.Normal_ed{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    color: #9DD335;
}
.Critical{
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
}
.Critical_acked{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
    border:solid 2px #CC0000;
}
.Critical_ed{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    color: #CC0000;
}

.Minor{
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
}
.Minor_acked{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
    border:solid 2px #47C3F2;
}
.Minor_ed{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    color: #47C3F2;
}

.Warning{
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
}
.Warning_acked{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
    border:solid 2px #FFA90B;
}
.Warning_ed{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    color: #FFA90B;
}

.Informational{
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
}
.Informational_acked{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
    border:solid 2px #BCBCBC;
}
.Informational_ed{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    color: #BCBCBC;
}

.Unknown{
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
}
.Unknown_acked{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
    border:solid 2px #A594B9;
}
.Unknown_ed{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    color: #A594B9;
}
.Major{
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
}
.Major_acked{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    border-radius:5px 5px 5px 5px;
    border:solid 2px #FF7415;
}
.Major_ed{
    width: 85px;
    display: inline-block;
    padding: 3px 2px;
    color: #FF7415;
}*/
.form-control {
	font-size: 10px !important;
}



.tipFooter{
    margin-top: 30px !important;
    margin-left: 50px !important;
    width: 350px !important;
}
.tipFooter span{
    color: red;
}
.modal-body{
    padding: 10px;
}
.form-horizontal .form-group {
    width: 450px !important;
}
.modal-tol .form-horizontal .form-group {
    width: 300px !important;
}
.searchUser .form-group{
    width: 300px !important;
}
@media screen and (min-width: 768px) {
    .modal-dialog {
        width: 900px !important;
    }
    .modal-tol{
        width: 500px !important;
    }
}
@media screen and (min-width: 768px) {
    .modal-dial {
        width: 500px !important;
    }
    .modal-tol{
        width: 500px !important;
    }
}
.date{
    margin: 0 -20px !important;
}
/*select下拉多选*/
.multiselect-container>li>a>label>input[type=checkbox] {
    margin-left: -20px;
    height:20px;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:focus, .dropdown-menu>.active>a:hover {
    background-color: #3E97FF;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    background-color: #fff;
    border-color: #ddd;
}

.btn-default:active, .btn-default:focus, .btn-default:hover,.btn-default {
    background-color: #fff;
    border-color: #ddd;
    color: #aaa;
}
/*角色组下拉菜单多选*/
.ms-choice{
    width: 0;
    height: 0;
    border: none;
}
.ms-drop {
    margin-left: -12px;
    border: 1px solid #e5e6e7;
}
.ms-choice > span {
    height: 34px;
    line-height: 34px;
}
.form-search{
    height: 25px;
}
.form-horizontal{
    margin-bottom: 20px;
}
.float-e-margins .btn{
    margin-top: -15px;
}
.datepicker{
    z-index: 10000000 !important;
}
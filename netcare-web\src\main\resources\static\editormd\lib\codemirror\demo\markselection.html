<!doctype html>

<title>CodeMirror: Selection Marking Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../addon/search/searchcursor.js"></script>
<script src="../addon/selection/mark-selection.js"></script>
<style>
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
      .CodeMirror-selected  { background-color: blue !important; }
      .CodeMirror-selectedtext { color: white; }
      .styled-background { background-color: #ff7; }
    </style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Selection Marking</a>
  </ul>
</div>

<article>
<h2>Selection Marking Demo</h2>
<form><textarea id="code" name="code">
Select something from here. You'll see that the selection's foreground
color changes to white! Since, by default, CodeMirror only puts an
independent "marker" layer behind the text, you'll need something like
this to change its colour.

Also notice that turning this addon on (with the default style) allows
you to safely give text a background color without screwing up the
visibility of the selection.</textarea></form>

    <script>
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
  lineNumbers: true,
  styleSelectedText: true
});
editor.markText({line: 6, ch: 26}, {line: 6, ch: 42}, {className: "styled-background"});
</script>

    <p>Simple addon to easily mark (and style) selected text. <a href="../doc/manual.html#addon_mark-selection">Docs</a>.</p>

  </article>

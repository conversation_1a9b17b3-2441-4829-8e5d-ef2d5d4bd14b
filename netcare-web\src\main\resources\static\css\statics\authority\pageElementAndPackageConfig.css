.blue_btn {
    display: inline-block;
}
.tabs-header,.tabs-header, .tabs-scroller-left, .tabs-scroller-right,
.tabs-tool, .tabs, .tabs-panels, .tabs li a.tabs-inner,
.tabs li.tabs-selected a.tabs-inner, .tabs-header-bottom .tabs li.tabs-selected a.tabs-inner,
.tabs-header-left .tabs li.tabs-selected a.tabs-inner, .tabs-header-right .tabs li.tabs-selected a.tabs-inner {
    border-color: #ddd;
}
.tabs-header{
    background-color: #f4f4f4;
}
.tabs li a.tabs-inner{
    border:none;
}
.tabs>li{
    border-right: 1px solid #ddd;
    margin: 0;
    padding:3px 5px;
}
.tabs-container .panel-body{
    border:none;
}
.tabs{
    border:none;
}
.tabs li a.tabs-inner,.tabs li.tabs-selected a.tabs-inner{
    background:none;
    border:none;
}
.tabs li.tabs-selected a.tabs-inner{
    color:#1BB394;
}
.model_mode_right{
    background-color: #e7e7e7;
    padding: 15px;
}
.pageElement{
    text-align: left;
}
.control-label{
    font-weight: 400;
    font-size:14px;
}
.form-group{
    float: left;
    height: 34px;
    line-height: 34px;
    margin-right: 50px;
}
.propItem{
    padding-left: 0;
}
.form-control[disabled]{
    background-color: #fff;
}
.pageEmlent{
    background-color: #fff;
}
.jstree-open>.jstree-anchor>.fa-folder:before{
    content:"\f07c"
}
.jstree-default .jstree-icon.none{
    width:0;
}
.ibox-content{
    border:1px solid #ddd;
}
.fl{
    float: left;
}
.fr{
    float: right;
}
.roleGroup_top{
    margin-bottom: 10px;
}

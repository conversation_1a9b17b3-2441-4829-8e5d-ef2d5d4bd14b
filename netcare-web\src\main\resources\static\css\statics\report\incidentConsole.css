.Confi_mode .btn {
    margin: 5px 0 0 20px;
}

.form-search {
    height: 25px;
}

.form-horizontal {
    margin-bottom: 20px;
}

.Oper_div {
    background: #3e97ff;
    line-height: 40px;
}

.Oper_div span {
    /*margin: 3px 20px;*/

    color: #fff;
    padding: 0 20px;
    font-weight: 700;
    cursor: pointer;
}
.Oper_div span:hover{
    text-decoration: underline;
}
#incidentConsole-form .form-control,#reportTaskAdd-form .form-control{
    margin-left: 10px;
}
#incidentConsole-form .input-group-btn .btn-default{
    margin-left: 10px;
    margin-right: -10px !important;
}
.modal-header button {
    margin-top: -2px;
    -webkit-appearance: none;
    padding: 0;
    cursor: pointer;
    background: 0 0;
    border: 0;
    float: right;
    font-size: 21px;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    filter: alpha(opacity=20);
    opacity: .2;
}
.panel-tool-restore {
    background: url(../../../img/panel_tools.gif) no-repeat -16px -16px;
}
.panel-tool-max {
    background: url(../../../img/panel_tools.gif) no-repeat 0px -16px;
}
.showRepo {
    width: 100%;
    left: 0;
    top: 0;
    margin: 0;
    padding: 0;
}
.showRepo .modal-header {
    padding: 5px 15px;
}


.x-toolbar {
    background-color: #d0d0d0;
    overflow-x: hidden;
}
.x-toolbar td {
    vertical-align: middle;
}
.fr-btn {
    font: normal 11px tahoma,verdana,helvetica;
    cursor: pointer;
    white-space: nowrap;
}
.ui-state-disabled {
    cursor: default!important;
    opacity: .5;
    filter: Alpha(Opacity=50);
    -moz-opacity: .5;
    background-image: none;
    width: 100%;
}
.fr-btn td {
    padding: 0!important;
}
.fr-btn-tl {
    width: 3px;
    height: 3px;
    background: no-repeat 0 0;
}
.fr-btn-tr i, .fr-btn-tl i, .fr-btn-br i, .fr-btn-bl i {
    height: 3px;
}
.fr-btn-tr i, .fr-btn-tl i, .fr-btn-mr i, .fr-btn-ml i, .fr-btn-br i, .fr-btn-bl i {
    font-size: 1px;
    line-height: 1px;
    width: 3px;
    display: block;
    overflow: hidden;
}
.x-toolbar .sep {
    display: block;
    font-size: 1px;
    height: 9px;
    width: 1px;
    overflow: hidden;
    cursor: default;
    margin: 0 2px 0;
    border: 0;
    background: #999;
}
.fr-btn-text-icon .fr-btn-icon-small-left .fr-btn-text {
    background-position: 0 center;
    background-repeat: no-repeat;
    padding-left: 18px;
    line-height: 15px;
    height: 15px;
}
.ui-state-disabled .fr-btn button, .ui-state-disabled .fr-btn-ml, .ui-state-disabled .fr-btn-mc, .ui-state-disabled .fr-btn-mr, .ui-state-disabled .fr-btn-tc, .ui-state-disabled .fr-btn-bc {
    cursor: default;
}
.fr-btn button {
    height: 15px;
    font: normal 12px arial,tahoma,verdana,helvetica;
    color: #333;
}
.fr-btn button {
    border: 0 none;
    background: transparent;
    padding-left: 3px;
    padding-right: 3px;
    cursor: pointer;
    margin: 0;
    overflow: hidden!important;
    overflow: visible;
    width: auto;
    -moz-outline: 0 none;
    outline: 0 none;
}
.x-emb-first {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -54px !important;
}
.fr-btn-text {
    cursor: pointer;
    white-space: nowrap;
    padding: 0;
}
.fr-btn-mc {
    background: repeat-x 0 -1096px;
    vertical-align: middle;
    text-align: center;
    padding: 0 5px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
}
.x-emb-previous {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -72px !important;
}
.fr-texteditor {
    height: 18px;
    line-height: 18px;
    vertical-align: middle;
    padding: 1px 3px!important;
    border: 1px solid #b5b8c8;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0;
    font: normal 12px tahoma,arial,helvetica,sans-serif;
}
.fr-label {
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
    text-align: left;
    vertical-align: middle;
    font: normal 12px tahoma,arial,helvetica,sans-serif;
}
.x-emb-next {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -90px !important;
}
.x-emb-last {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -108px !important;
}
.x-emb-flashprint {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -162px !important;
}
.x-emb-print {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -377px !important;
}
.x-emb-export {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -395px !important;
}
.x-emb-email {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -413px !important;
}
pre{
    background: none;
    border:none;
    padding:0;
}
.x-emb-pdfprint {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -180px !important;
}
.x-emb-appletprint {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -198px !important;
}
.x-emb-flashprint {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -162px !important;
}
.x-emb-pdf {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -216px !important;
}
.x-emb-excel {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -234px !important;
}
.x-emb-word {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -252px !important;
}
.x-emb-image {
    background-image: url(../../../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -270px !important;
}
.navbar{
    min-height: 0;
    margin-bottom: 0;
}
.navbar-default .navbar-nav>.open>a, .navbar-default .navbar-nav>.open>a:focus, .navbar-default .navbar-nav>.open>a:hover ,.navbar-default .nav>li>a:focus, .navbar-default .nav>li>a:hover{
    color: #666;
    background-color: #d0d0d0;
}
.nav>li>a{
    padding: 0;
}
.showRepo .modal-body{
    padding: 0;
}
.navbar /* .dropdown-menu */{
    position: fixed;
    top:60px;
    left: 533px;
}
/* #print .dropdown-menu{
    left: 447px;
} */
.input-group-btn .btn{
    padding: 6px 5px !important;
    margin-top: 0px;
    margin-top: -7px !important;
    margin-right: 0 !important;
    margin-left: 0;
}
.input-group-btn .dropdown-menu{
    left: 0 !important;
    top:95%;
    width: 454px;
}
#incidentConsole-form tr{
    line-height: 40px;
}
.dropdown-menu>li>a{
    line-height: 12px;
}
.dropdown-menu>li>a:focus, .dropdown-menu>li>a:hover {
     background-color: none;
}
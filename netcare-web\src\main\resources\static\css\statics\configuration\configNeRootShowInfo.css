#ios,#syban {
	border: 1px solid #CCCCCC;
	border-top: none;
}
#ios p {
	border: none;
	background: none;
}
.conTop {
	border: 1px solid #ccc;
	border-top: none;
}
.conBot {
	padding-left: 0;
	margin-top: 20px;
	border: 1px solid #ccc;
}
.conTop p {
	border: none;
}
.topHead {
	padding-left: 20px;
	font-weight: 600;
	line-height: 30px;
	border: 1px solid #ccc;
	background: #eee;
}
.tabBcon {
	padding-left: 0;
}
.regionName {
    display: block;
    font-weight: 600;
    padding: 7px 12px;
}
.eventHistory,.eventBackUp,.Service_line {
	padding-left: 0;
}
#menuAdd input {
	border: none;
	outline: none;
}

#using_json {
	margin-top: 20px;
}
.blk {
	display: block;
}
.bln {
	display: none !important;
}
.customerAddModal{
    width: 940px;
    margin-top: -2px;
}
.modal-dialog .form-group{
    width: 445px;
    margin: 5px -5px;
}
.radio{
    display: inline-block;
}
.checkb{
    height: 34px;
}
input.help{
    display: inline-block;
    /*padding: 4px;*/
}
.upload{
    padding: 4px 2px;
}
form.m-t{
    width:598px;
}
.HeaderButton {
	display: none;
}
.expanded {
	width: 30px;
	height: 248px;
	position: absolute;
	top: -32px;
	right: 0;
	border-left: 2px solid #CCCCCC;
	z-index: 999;
	background: #fff;
}
.collapsed {
	width: 210px;
	height: 160px;
	position: absolute;
	top: -32px;
	right: 0;
	border-left: 2px solid #CCCCCC;
}
.collapsed .titleTool {
	width: 100%;
	height: 31px;
	line-height: 31px;
	padding-left: 10px;
	font-weight: 600;
}
.collapsed .contColla {
	color: #f00;
	padding-left: 8px;
	font-size:14px;
	list-style: none;
	line-height: 20px;
}
.contColla li {
	cursor: pointer;
}
.contColla li:hover {
	color: deepskyblue;
}
.contPing {
	height: 300px;
}
.contPing p {
	line-height: 36px;
}
.netImg {
	width: 86px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	-moz-border-radius:10px;
	-ms-border-radius:10px;
	-o-border-radius:10px;
	border-radius: 10px;
	cursor: pointer;
	margin-left: 20px;
	color: #fff;
}

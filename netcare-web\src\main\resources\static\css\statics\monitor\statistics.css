.leftTitle{
    height: 36px;
    background: #3e97ff;
    color: #fff;
    line-height: 36px;
    font-weight: 700;
    margin-left: -15px;
    padding-left: 20px;
}
.dataRight .form-group{
    height:24px;
}
.statisticLeft{
    border: 1px solid #ddd;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}
.statisticLeft,.statisticRight{
    height: 640px;
}
.statisticRight{
    overflow-y: auto;
    overflow-x: hidden;
}
.statisticLeft .form-group{
    margin-top: 15px;
    margin-bottom: 60px;
    padding-right: 10px;
}
#startTime,#endTime{
    padding:0;
}
.statisticLeft .form-group label{
    line-height: 34px;
}
.statisticLeft .search{
    margin-top:10px;
    margin-left: 185px;
}
.statisticRight .ibox{
    margin-bottom: 0 !important;
}
#searchTime a,#dataAnalysis a{
    text-decoration: underline;
    margin: 10px;
}
.echarts{
    height: 347px;
    background-color: #00a2d4;
}
.modal-bod{
    height: 350px;
}
.modal-dial{
    width: 950px;
}
.time{
    padding: 0 50px;
}
.time a{
    margin: 5px;
    text-decoration: underline;
}
#dataExportSource{
    overflow-y: auto;
}
.btn-cli{
    text-align: right;
}
.dataSourse a{
    text-decoration: underline;
    font-size: 16px;
}
.dataSourse div{
    margin-bottom: 15px;
}
.neInfoCon{
    line-height: 32px;
    float: left;
    margin-right: 5px;
}
.neInfoCon,.neInfoConRight>div{
    border: 1px solid #ddd;
    border-bottom: none;
    padding:0 10px;
}
.chartTab .ibox-con {
    padding: 15px 0 20px !important;
}
.neInfoCon>div{
    border-bottom: 1px solid #ddd;
}
.neDesc{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
#pysicalLinkTree{
    height: 459px;
    overflow-y: scroll;
}
#neInfoCon span,#neInfoConRight span{
    padding-left: 10px;
}
.ui-jqgrid .ui-jqgrid-bdiv { 
	border-top: 1px solid #E1E1E1; 
	overflow-x: auto; 
} 
.frozen-div, .frozen-bdiv {
	background-color: #E4E6E9;/*与网页背景色一致*/
} 
#pagination{
    margin: 12px 0 0 12px;
    height: 34px;
}
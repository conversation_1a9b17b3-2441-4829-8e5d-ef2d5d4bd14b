.researchDiagnose{
    display: none;
}

#stepNe{
    height: 18px;
    display: block;
    font-weight: 700;
    padding-left: 80px;
}
.deviceWid .form-group{
    width:260px ;
}
#stepNe,#fillTipForSolve{
    color: red;
    font-weight: 700;
}
.workmeth{
    margin-top: 10px;
}
#time .form-control{
    margin-top: 10px !important;
}

#relationMod .statusSea{
    margin: 0;
}
#relationMod .statusSea>div:first-child{
    margin-left: -65px;
}
#relationMod .statusSea>div:last-child{
    margin-left: -20px;
}
#relationMod .form-group{
    width: 538px;
}
.relationAddSea{
    float: right;
    margin-top: -54px;
}
.workmeth input.form-control{
    height: 26px !important;
}
#knowledgeP .form-group{
    margin-left: 10px !important;
}
.pignose-tab-wrapper .pignose-tab-container{
    top:36px;
    padding: 0 !important;
}
.nav-tabs>li>a{
    padding: 8px 5px !important;
    font-weight: 400 !important;
}
#deviceAdd .modal-body {
    padding: 0 28px;
}
#sousuoLabel .form-control {
    height: 26px;
}
#sousuoLabel{
    margin-bottom: 10px !important;
}
.pignose-tab-wrapper .pignose-tab-group{
    margin-top: 10px !important;
}
.Step .Tab_shinc li{
    float: left;
    font-size:14px;
    color: #91a6b8;
    line-height: 89px;
    list-style: none;
}
#menuControlTab td {
    white-space: nowrap;
}
.table_inc td {
    white-space: nowrap;
}

.Step .Tab_shinc{
    height: 43px;
}
.Step .Tab_shinc li{
    float: left;
    white-space: nowrap;
    color: #A3A3A3;
    font-weight: 700;
    line-height: 64px;
}
#incdtNew div,#DiagnoseNew div{
    margin-left: -26px;
}
#incdtthree div,#Diagnosethree div{
    margin-left: -25px;
}
#incdtfour div,#Diagnosefour div{
    margin-left: -22px;
}
#incdtfive div,#Diagnosefive div{
    margin-left: -19px;
}
.T_1,.T_2,.T_3,.T_4{
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #A3A3A3;
    margin-top: 6px;
    display: block;
}
.incdtNewLine,.incdtthreeLine,.incdtfourLine{
    width: 25%;
    height: 1px;
    border: 1px solid #A3A3A3;
    margin-top: 12px;
    opacity: 0.9;
}

#incdtNew span,#incdtthree span, #incdtfour span, #incdtfive span,#DiagnoseNew span,#Diagnosethree span, #Diagnosefour span, #Diagnosefive span {
    width: 44px;
    height: 52px;
    display: block;
    color: #3E97FF;
    background: url(../../../img/Tab_icon1.jpg) no-repeat -270px -11px;
    margin-top: -5px;
}
#incdtNew span,#DiagnoseNew span{
    background: url(../../../img/Tab_icon1.jpg) no-repeat -32px -11px;
}
#incdtNew,#incdtthree,#incdtfour,#incdtfive,#DiagnoseNew,#Diagnosethree,#Diagnosefour,#Diagnosefive{
    margin-left: -26px;
}
#deviceC .form-horizontal{
    margin-bottom: 0 !important;
}
#solve select{
    height: 26px;
    padding:0;
    margin-left: 8px;
    line-height: 26px;
}
.lineH{
    line-height: 33px !important;
}
#time .form-control{
    height: 30px;
}
.Oper_div {
    background: #3E97FF;
    overflow: hidden;
    clear: both;
    color: #fff;
}
.Oper_div td a:hover {
    color: #fff;
    text-decoration: underline;
}
.Oper_div td a {
    color: #fff;
    display: inline-block;
    height: 28px;
    line-height: 28px;
    padding-left: 20px;
    margin: 0px 3px;
}
#dataNumber{
    z-index: 2051 !important;
}
.next_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px 0px;
}
.req_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -140px;
}
.get_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -56px;
}
.rep_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -336px;
}
.save_btn{
    background: url(../../../img/Oper_btn.png) no-repeat 0px -85px;
}
.Rollback_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -280px;
}
.Cancel_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -308px;
}
.Mission_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -392px;
}
.hang_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -112px;
}
.Relieve_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -448px;
}
.Accep_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -28px;
}
.Exc_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -196px;
}
.email_btn {
    background: url(../../../img/Oper_btn.png) no-repeat 0px -168px;
}
#hangModal .form-control{
    margin-bottom: 5px;
}
#menuControlTab a{
    color: #fff;
    display: inline-block;
    padding-left: 20px;
    margin: 0px 3px;
}
#menuControlTab .progress-bar-default {
    height: 15px !important;
}
.pignose-tab-wrapper .pignose-tab-btn{
    min-width:0;
    padding: 0 0.5em;
}
#menuControlTab .progress,#DiagnoseTab .progress{
    margin-bottom: 0 !important;
}

#menuControlTab .progress-bar,#DiagnoseTab .progress-bar {
    background-color: #c0efb8;
}
#menuControlTab .progress-bar {
    line-height: 16px;
    margin-left: 5px;
}
.dashboardTime .progress-bar{
    line-height: 14px;
    background-color: #ddd;
}
.dashboardTime .progress-bar-success {
    background-color: #5CB85C;
}
.dashboardTime .progress-bar-warning {
    background-color: #F0AD4E;
}
.dashboardTime .progress-bar-danger {
    background-color: #D9534F;
}
.dashboardTime .progress{
    height: 14px;
}

.inc_hr {
    border-top: 1px dashed #c7c7c7;
    width: 95%;
    height: 5px;
    margin: 10px auto;
}
.table_inc td {
    line-height: 26px;
    color: #09121a;
}
.table_inc td, .table_inc th {
    padding: 0px 5px;
    text-align: left;
}
.font_red {
    color: #fe0e0e;
    font-size:14px;
    font-weight: 700;
}
#companyCombByHandle, #helpBtn {
    float: left;
}
#helpBtn{
    height: 24px;
    line-height: 20px;
}
input.inc_input {
    width: 142px;
    border: 1px solid #b3b6b9;
    height: 20px;
}
.table_inc .form-control{
    height: 25px;
    padding: 0;
}
input#occurTime {
    font-size:14px;
    padding-left: 0;
}
.ibox-content{
    padding: 0;
}
.pignose-tab-wrapper .pignose-tab-btn.active {
    color: #fff;
    background-color: #3E97FF;
}
.pignose-subTab-btn.active {
    color: #555 !important;
    background-color: #fff !important;
}

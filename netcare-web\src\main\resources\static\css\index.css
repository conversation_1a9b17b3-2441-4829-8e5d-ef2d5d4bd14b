.navbar-right>li {
    line-height: 36px;
    margin-top: 14px;
}

#side-menu>li>a {
    padding: 4px 16px 4px 20px;
}

.clearfix:after,
.clearfix:before {
    content: " ";
    display: block;
    clear: both;
    height: 0;
}

.nav>li>a i {
    width: 26px;
    height: 38px;
    display: inline-block;
    opacity: 0.7;
    vertical-align: middle;
}

.navbar-right>li>a i {
    height: 15px;
    vertical-align: middle;
}

.navbar-right>li>a {
    padding: 0 10px !important;
    margin-top: 0;
}

.nav>li>a:hover i {
    vertical-align: middle;
    width: 26px;
    display: inline-block;
    opacity: 1;
}

.navbar-top-links li a {
    min-height: 43px;
}

.m_1 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px 0px;
}

.m_2 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -33px;
}

.m_3 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -66px;
}

.m_4 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -99px;
}

.m_5 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -132px;
}

.m_6 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -165px;
}

.m_7 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -198px;
}

.m_8 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -231px;
}

.m_9 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -265px;
}

.m_10 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -298px;
}

.m_11 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -331px;
}

.m_12 a i {
    background: url(../img/m_li_bg_black.png) no-repeat 0px -364px;
}

.majorController {
    position: relative;
}

.majorController .Major_01 {
    position: absolute;
    left: 120px;
    top: 5px;
    font-size: 14px;
}

.majorController .Major_01>a {
    top: 24px;
    left: -20px;
    position: absolute;
}

.majorController .networkSRC {
    left: 215px;
}

.majorController .standrad {
    left: 12%;

}

.majorController .top_r {
    position: absolute;
    z-index: 6;
    right: 20px;
    color: #999;
    top: 30px;
}

/*ITIL报表控制台显示报表与下载报表样式*/
#showReportModal .Oper_div {
    background: #3e97ff;
    line-height: 40px;
}

#showReportModal .Oper_div span {
    /*margin: 3px 20px;*/
    text-decoration: underline;
    color: #fff;
    padding: 0 20px;
    font-weight: 700;
    cursor: pointer;
}

.nav-second-level,
.nav-third-level,
.active a {
    /*background-color: #2f4050;*/
    background-color: #fcfdff;
}

#showReportModal .modal-header button {
    margin-right: 10px;
    -webkit-appearance: none;
    padding: 0;
    cursor: pointer;
    border: 0;
    float: right;
    font-size: 21px;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    filter: alpha(opacity=50);
    opacity: .5;
    width: 16px;
    height: 16px;
}

.modal-restore {
    background: url(../img/panel_tools.gif) no-repeat -16px -16px;
}

.modal-max {
    background: url(../img/panel_tools.gif) no-repeat 0px -16px;
}

.showRepo {
    width: 100%;
    left: 0;
    top: 0;
    margin: 0;
    padding: 0;
}

.maxReport {
    width: 538px;
    height: 400px;
    left: 0;
    top: 20px;
    bottom: 0;
    right: 0;
    padding: 0;
    overflow-x: auto;
    overflow-y: hidden;
}

#showReportModal .modal-header {
    padding: 5px 15px;
}


#showReportModal .x-toolbar {
    background-color: #d0d0d0;
    overflow-x: hidden;
}

#showReportModal .x-toolbar td {
    vertical-align: middle;
}

#showReportModal .fr-btn {
    font: normal 11px tahoma, verdana, helvetica;
    cursor: pointer;
    white-space: nowrap;
}

#showReportModal .ui-state-disabled {
    cursor: default !important;
    opacity: .5;
    filter: Alpha(Opacity=50);
    -moz-opacity: .5;
    background-image: none;
    width: 100%;
}

#showReportModal .fr-btn td {
    padding: 0 !important;
}

#showReportModal .fr-btn-tl {
    width: 3px;
    height: 3px;
    background: no-repeat 0 0;
}

#showReportModal .fr-btn-tr i,
#showReportModal .fr-btn-tl i,
#showReportModal .fr-btn-br i,
#showReportModal .fr-btn-bl i {
    height: 3px;
}

#showReportModal .fr-btn-tr i,
#showReportModal .fr-btn-tl i,
#showReportModal .fr-btn-mr i,
#showReportModal .fr-btn-ml i,
#showReportModal .fr-btn-br i,
#showReportModal .fr-btn-bl i {
    font-size: 1px;
    line-height: 1px;
    width: 3px;
    display: block;
    overflow: hidden;
}

#showReportModal .x-toolbar .sep {
    display: block;
    font-size: 1px;
    height: 9px;
    width: 1px;
    overflow: hidden;
    cursor: default;
    margin: 0 2px 0;
    border: 0;
    background: #999;
}

#showReportModal .fr-btn-text-icon .fr-btn-icon-small-left .fr-btn-text {
    background-position: 0 center;
    background-repeat: no-repeat;
    padding-left: 18px;
    line-height: 15px;
    height: 15px;
}

#showReportModal .ui-state-disabled .fr-btn button,
#showReportModal .ui-state-disabled .fr-btn-ml,
#showReportModal .ui-state-disabled .fr-btn-mc,
#showReportModal .showRepo .ui-state-disabled .fr-btn-mr,
#showReportModal .ui-state-disabled .fr-btn-tc,
#showReportModal .ui-state-disabled .fr-btn-bc {
    cursor: default;
}

#showReportModal .modal-body button {
    height: 15px;
    font: normal 12px arial, tahoma, verdana, helvetica;
    color: #333;
    border: 0 none;
    background: transparent;
    padding-left: 3px;
    padding-right: 3px;
    cursor: pointer;
    margin: 0;
    overflow: hidden !important;
    overflow: visible;
    width: auto;
    -moz-outline: 0 none;
    outline: 0 none;
}

.x-emb-first {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -54px !important;
}

#showReportModal .fr-btn-text {
    cursor: pointer;
    white-space: nowrap;
    padding: 0;
}

#showReportModal .fr-btn-mc {
    background: repeat-x 0 -1096px;
    vertical-align: middle;
    text-align: center;
    padding: 0 5px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
}

#showReportModal .x-emb-previous {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -72px !important;
}

#showReportModal .fr-texteditor {
    height: 18px;
    line-height: 18px;
    vertical-align: middle;
    padding: 1px 3px !important;
    border: 1px solid #b5b8c8;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0;
    font: normal 12px tahoma, arial, helvetica, sans-serif;
}

#showReportModal .fr-label {
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
    text-align: left;
    vertical-align: middle;
    font: normal 12px tahoma, arial, helvetica, sans-serif;
}

.x-emb-next {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -90px !important;
}

.x-emb-last {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -108px !important;
}

.x-emb-flashprint {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -162px !important;
}

.x-emb-print {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -377px !important;
}

.x-emb-export {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -395px !important;
}

.x-emb-email {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -413px !important;
}

#showReportModal pre {
    background: none;
    border: none;
    padding: 0;
}

.x-emb-pdfprint {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -180px !important;
}

.x-emb-appletprint {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -198px !important;
}

.x-emb-flashprint {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -162px !important;
}

.x-emb-pdf {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -216px !important;
}

.x-emb-excel {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -234px !important;
}

.x-emb-word {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -252px !important;
}

.x-emb-image {
    background-image: url(../img/ReportServer.png) !important;
    background-repeat: no-repeat !important;
    background-position: 0px -270px !important;
}

#showReportModal .navbar {
    min-height: 0;
    margin-bottom: 0;
}

#showReportModal .navbar-default .navbar-nav>.open>a,
#showReportModal .navbar-default .navbar-nav>.open>a:focus,
#showReportModal .navbar-default .navbar-nav>.open>a:hover,
#showReportModal .navbar-default .nav>li>a:focus,
#showReportModal .navbar-default .nav>li>a:hover {
    color: #666;
    background-color: #d0d0d0;
}

#showReportModal .nav>li>a {
    padding: 0;
}

#showReportModal .modal-body {
    padding: 0;
}

#showReportModal .navbar .dropdown-menu {
    position: fixed;
    top: 60px;
    left: 533px;
}

#print .dropdown-menu {
    left: 447px;
}

#showReportModal .dropdown-menu>li>a {
    line-height: 12px;
}

#showReportModal .dropdown-menu>li>a:focus,
#showReportModal .dropdown-menu>li>a:hover {
    background-color: none;
}

.wand {
    /* display: block;
    margin-left: 5px; */
    display: none;
}
/* 新版ui  23-6-29 */
.navbar-static-top {
    background: #3e97ff !important;
}

.btn-info {
    background-color: #3474c2;
    border-color: #3474c2;
    color: #bcd0ea;
}

.btn-info.active,
.btn-info:active,
.btn-info:focus,
.btn-info:hover,
.open .dropdown-toggle.btn-info {
    background-color: #3474c2 !important;
    border-color: #3474c2 !important;
    color: #bcd0ea !important;
}
.adminnum{
    font-size: 14px;
    padding-top: 5px;
    display: flex;
}
.adminnum label{
    font-weight: normal !important;
}
textarea{
    background: #fff !important;
}
.nav-header{
    background: #3e97ff !important;
}
.sidebar-collapse{
    background: #fcfdff !important;
}
.nav>li>a{
    color: #1d2129 !important;
}
.navbar-default .nav>li>a:focus, .navbar-default .nav>li>a:hover{
    background-color: #ecf5ff;
}
.modal-header{
    background-color: #3e97ff !important;
}
.locationSearchT{
    background: #fff !important;
}
textarea{
    background: #fff !important;
}
.nav-header{
    height: 70px;
    border-bottom: 1px solid #e8ecf5;
    background-color: #3e97ff !important;
    position: fixed;
    top: 0px;
    z-index: 9999;
    width: 220px;
}
#side-menu{
    margin-top: 70px;
    font-size: 14px;
}
.vipNum{
    display: none;
}
.standrad{
    display: block;
}

@media (min-width: 768px) {
    .navbar-right {
        width: unset !important;
    }
}
.navbar-header{
    display: none !important;
}

.hangApply{
    margin-top: 10px;
    margin-bottom: 5px;
    padding-left: 30px;
}
.red{
    color: red;
}
.displayNoneG1{
    display: none;
}
.problemTi,.shiftTi{
    display: none;
    color: red;
    text-align: center;
}
.hangApply>button{
    padding: 3px 5px;
}
.tool>span {
    padding: 4px 5px;
    border-radius: 3px;
    background: #3E97FF;
    color: #fff;
    cursor: pointer;
}
@media (min-width: 768px){
    .shiftModal {
        width: 900px;
    }

}
.searchUser .form-group {
    float: left;
    width: 360px;
    margin:5px -48px;
}
.taskTi {
	color: #f00;
}
.subcont {
    width: 60px;
    height: 36px;
    text-align: center;
    line-height: 26px;
    cursor: pointer;
    border-radius: 4px;
}
#startDate,#endDate {
	font-size: 15px;
}
.subcont .submit {
    border: none;
    background: none;
    outline: none;
}
.removeBtn {
    /*color: #ddd;*/
}
.table_inc td {
    padding: 0 5px;
}
.blk {
    display: block !important;
}
.customerAddModal {
    width: 940px;
    height: 400px;
    overflow: auto;
}
.detailc {
    width: 100%;
    border-width: 2px;
    border-style: groove;
    border-color: threedface;
    border-image: initial;
}
.detailc legend {
    border-bottom: none;
    font-size:14px;
    width: 78px;
}
.table_inc {
    margin-bottom: 10px;
}
.detailc1 .form-control {
    margin-top: 3px;
}
.hidC {
    width: 100%;
    border-top: 1px dashed #ccc;
    padding-top: 10px;
    display: none;
}
.showinfo {
    background: #3E97FF;
    color: #fff;
    font-size: 14px;
    padding: 3px 4px;
    border-radius: 3px;
}
.font_red {
    color: #f00;
}

#new_deskform legend{
    font-size:14px;
    font-weight: 700;
}
#new_deskform select,#new_deskform input{
    padding: 0;
    height: 26px;
    padding: 0 2px;
    margin: 3px 0;
}

#createRequest .form-control{
    height: 26px;
    padding: 0 2px;
    margin: 2px 0;
}
.inc1_hr {
    border-top: 1px dashed #c7c7c7;
    width: 95%;
    height: 5px;
    margin: 10px auto;
}

<!doctype html>

<title>CodeMirror: Release History</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="docs.css">
<script src="activebookmark.js"></script>

<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a class=active data-default="true" href="#v5">Version 5.x</a>
    <li><a href="#v4">Version 4.x</a>
    <li><a href="#v3">Version 3.x</a>
    <li><a href="#v2">Version 2.x</a>
    <li><a href="#v1">Version 0.x</a>
  </ul>
</div>

<article>

<h2>Release notes and version history</h2>

<section id=v5 class=first>

  <h2>Version 6.x</h2>

  <p>See <a href="https://codemirror.net/docs/changelog/">the new website</a>.</p>

  <h2>Version 5.x</h2>

  <p class="rel">20-11-2023: <a href="https://codemirror.net/5/codemirror-5.65.16.zip">Version 5.65.16</a>:</p>

  <ul class="rel-note">
    <li>Fix focus tracking in shadow DOM.</li>
    <li><a href="https://codemirror.net/5/mode/go/">go mode</a>: Allow underscores in numbers.</li>
    <li><a href="https://codemirror.net/5/mode/jsx/index.html">jsx mode</a>: Support TS generics marked by trailing comma.</li>
  </ul>

  <p class="rel">29-08-2023: <a href="https://codemirror.net/5/codemirror-5.65.15.zip">Version 5.65.15</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_lint">lint addon</a>: Prevent tooltips from sticking out of the viewport.</li>
    <li><a href="https://codemirror.net/5/mode/yaml/">yaml mode</a>: Fix an exponential-complexity regular expression.</li>
  </ul>

  <p class="rel">17-07-2023: <a href="https://codemirror.net/5/codemirror-5.65.14.zip">Version 5.65.14</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/mode/clike/">clike mode</a>: Fix poor indentation in some Java code.</li>
    <li><a href="https://codemirror.net/5/mode/nsis/index.html">nsis mode</a>: Recognize <code>!assert</code> command.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_lint">lint addon</a>: Remove broken annotation deduplication.</li>
  </ul>

  <p class="rel">27-04-2023: <a href="https://codemirror.net/5/codemirror-5.65.13.zip">Version 5.65.13</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/mode/dart/index.html">dart mode</a>: Add some new keywords.</li>
    <li><a href="https://codemirror.net/5/mode/clike/">clike mode</a>: Tokenize Scala character literals.</li>
  </ul>

  <p class="rel">20-02-2023: <a href="https://codemirror.net/5/codemirror-5.65.12.zip">Version 5.65.12</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/mode/python/">python mode</a>: Add new built-ins and keywords.</li>
  </ul>

  <p class="rel">20-12-2022: <a href="https://codemirror.net/5/codemirror-5.65.11.zip">Version 5.65.11</a>:</p>

  <ul class="rel-note">
    <li>Also respect autocapitalize/autocorrect/spellcheck options in textarea mode.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_sql-hint">sql-hint addon</a>: Fix keyword completion in generic SQL mode.</li>
  </ul>

  <p class="rel">20-11-2022: <a href="https://codemirror.net/5/codemirror-5.65.10.zip">Version 5.65.10</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_sql-hint">sql mode</a>: Fix completion when the SQL mode is wrapped by some outer mode.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/index.html">javascript mode</a>: Fix parsing of property keywords before private property names.</li>
  </ul>

  <p class="rel">20-09-2022: <a href="https://codemirror.net/5/codemirror-5.65.9.zip">Version 5.65.9</a>:</p>

  <ul class="rel-note">
    <li>Add a workaround for a regression in Chrome 105 that could cause content below the editor to not receive mouse events.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Resize the tooltip if it doesn’t fit the screen.</li>
    <li><a href="https://codemirror.net/5/mode/swift">swift mode</a>: Fix tokenizing of block comments.</li>
    <li><a href="https://codemirror.net/5/mode/jinja2/">jinja2 mode</a>: Support line statements.</li>
  </ul>

  <p class="rel">20-08-2022: <a href="https://codemirror.net/5/codemirror-5.65.8.zip">Version 5.65.8</a>:</p>

  <ul class="rel-note">
    <li>Include direction override and isolate characters in the default set of special characters.</li>
    <li>Fix an issue that could cause document corruption when mouse-selecting during composition.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_foldgutter">foldgutter addon</a>: Refresh markers when the editor’s mode changes.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_merge">merge addon</a>: Fix syntax that prevented the addon from loading in IE10.</li>
  </ul>

  <p class="rel">20-07-2022: <a href="https://codemirror.net/5/codemirror-5.65.7.zip">Version 5.65.7</a>:</p>

  <ul class="rel-note">
    <li>Fix several references to the global <code>document</code>/<code>window</code>, improving support for creating an editor in another frame.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Use <a href="https://github.com/replit/codemirror-vim/">upstream</a> code instead of keeping our own copy.</li>
    <li><a href="https://codemirror.net/5/demo/tern.html">tern addon</a>: Properly HTML escape variable names in rename dialog.</li>
  </ul>

  <p class="rel">20-06-2022: <a href="https://codemirror.net/5/codemirror-5.65.6.zip">Version 5.65.6</a>:</p>

  <ul class="rel-note">
    <li>Avoid firing <code>beforeCursorEnter</code> callbacks twice for cursor selections.</li>
    <li>Improve support for auto-hiding macOS scrollbars.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Fix an issue where the tooltip could be placed to the left of the screen.</li>
    <li><a href="https://codemirror.net/5/mode/swift">swift mode</a>: Support structured concurrency keywords.</li>
  </ul>

  <p class="rel">30-05-2022: <a href="https://codemirror.net/5/codemirror-5.65.5.zip">Version 5.65.5</a>:</p>

  <ul class="rel-note">
    <li>Work around a bug in Chrome 102 that caused wheel scrolling of the editor to constantly stop.</li>
    <li><a href="https://codemirror.net/5/demo/search.html">search addon</a>: Make sure the search field has an accessible label.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_comment">comment addon</a>: Preserve indentation on otherwise empty lines when indenting.</li>
  </ul>

  <p class="rel">20-05-2022: <a href="https://codemirror.net/5/codemirror-5.65.4.zip">Version 5.65.4</a>:</p>

  <ul class="rel-note">
    <li>Ignore paste events when the editor doesn’t have focus.</li>
    <li><a href="https://codemirror.net/5/mode/sparql/index.html">sparql mode</a>: Fix parsing of variables after operators.</li>
    <li><a href="https://codemirror.net/5/mode/julia/">julia mode</a>: Properly tokenize <code>!==</code> and <code>===</code> operators.</li>
  </ul>

  <p class="rel">20-04-2022: <a href="https://codemirror.net/5/codemirror-5.65.3.zip">Version 5.65.3</a>:</p>

  <ul class="rel-note">
    <li>Fix a crash that could occur when when marking text.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_merge">merge addon</a>: Add aria label to buttons.</li>
    <li><a href="https://codemirror.net/5/mode/groovy/index.html">groovy mode</a>: Properly highlight interpolated variables.</li>
  </ul>

  <p class="rel">21-02-2022: <a href="https://codemirror.net/5/codemirror-5.65.2.zip">Version 5.65.2</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/mode/clike/">clike mode</a>: Recognize triple quoted string in Java.</li>
    <li><a href="https://codemirror.net/5/mode/cypher/index.html">cypher mode</a>: Fix handling of punctuation.</li>
  </ul>

  <p class="rel">20-01-2022: <a href="https://codemirror.net/5/codemirror-5.65.1.zip">Version 5.65.1</a>:</p>

  <ul class="rel-note">
    <li>Fix miscalculation of vertical positions in lines that have both line widgets and replaced newlines.</li>
  </ul>

  <p class="rel">20-12-2021: <a href="https://codemirror.net/5/codemirror-5.65.0.zip">Version 5.65.0</a>:</p>

  <ul class="rel-note">
    <li>brace-folding addon: Fix broken folding on lines with both braces and square brackets.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Support g0, g$, g&lt;Arrow&gt;.</li>
  </ul>

  <p class="rel">20-11-2021: <a href="https://codemirror.net/5/codemirror-5.64.0.zip">Version 5.64.0</a>:</p>

  <ul class="rel-note">
    <li>Fix a crash that occurred in some situations with replacing marks across line breaks.</li>
    <li>Make sure native scrollbars reset their position when hidden and re-shown.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Support C-u to delete back a line.</li>
  </ul>

  <p class="rel">11-10-2021: <a href="https://codemirror.net/5/codemirror-5.63.3.zip">Version 5.63.3</a>:</p>

  <ul class="rel-note">
    <li>Prevent external styles from giving the hidden textarea a min-height.</li>
    <li>Remove a stray autosave file that was part of the previous release.</li>
  </ul>

  <p class="rel">29-09-2021: <a href="https://codemirror.net/5/codemirror-5.63.1.zip">Version 5.63.1</a>:</p>

  <ul class="rel-note">
    <li>Fix an issue with mouse scrolling on Chrome 94 Windows, which made scrolling by wheel move unusably slow.</li>
  </ul>

  <p class="rel">20-09-2021: <a href="https://codemirror.net/5/codemirror-5.63.0.zip">Version 5.63.0</a>:</p>

  <ul class="rel-note">
    <li>Fix scroll position jumping when scrolling a document with very different line heights.</li>
    <li><a href="https://codemirror.net/5/mode/xml/">xml mode</a>: Look up HTML element behavior in a case-insensitive way.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Support guu for case-changing.</li>
  </ul>

  <p class="rel">20-08-2021: <a href="https://codemirror.net/5/codemirror-5.62.3.zip">Version 5.62.3</a>:</p>

  <ul class="rel-note">
    <li>Give the editor a <code>translate=no</code> attribute to prevent automatic translation from modifying its content.</li>
    <li>Give vim-style cursors a width that matches the character after them.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_merge">merge addon</a>: Make buttons keyboard-accessible.</li>
    <li><a href="https://codemirror.net/5/demo/emacs.html">emacs bindings</a>: Fix by-page scrolling keybindings, which were accidentally inverted.</li>
  </ul>

  <p class="rel">21-07-2021: <a href="https://codemirror.net/5/codemirror-5.62.2.zip">Version 5.62.2</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_lint">lint addon</a>: Fix a regression that broke several addon options.</li>
  </ul>

  <p class="rel">20-07-2021: <a href="https://codemirror.net/5/codemirror-5.62.1.zip">Version 5.62.1</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Make matching of upper-case characters more Unicode-aware.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_lint">lint addon</a>: Prevent options passed to the addon itself from being given to the linter.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Improve screen reader support.</li>
    <li><a href="https://codemirror.net/5/demo/search.html">search addon</a>: Avoid using <code>innerHTML</code>.</li>
  </ul>

  <p class="rel">21-06-2021: <a href="https://codemirror.net/5/codemirror-5.62.0.zip">Version 5.62.0</a>:</p>

  <ul class="rel-note">
    <a href="https://codemirror.net/5/doc/manual.html#addon_lint">lint addon</a>: Add support for highlighting lines with errors or warnings.</li>
    <li>Improve support for vim-style cursors in a number of themes.</li>
  </ul>

  <p class="rel">20-05-2021: <a href="https://codemirror.net/5/codemirror-5.61.1.zip">Version 5.61.1</a>:</p>

  <ul class="rel-note">
    <li>Fix a bug where changing the editor’s document could confuse text-direction management.</li>
    <li>Fix a bug in horizontally scrolling the cursor into view.</li>
    <li>Optimize adding lots of marks in a single transaction.</li>
    <li><a href="https://codemirror.net/5/demo/simplemode.html">simple mode addon</a>: Support regexps with a unicode flag.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/index.html">javascript mode</a>: Add support for TypeScript template string types, improve integration with JSX mode.</li>
  </ul>

  <p class="rel">20-04-2021: <a href="https://codemirror.net/5/codemirror-5.61.0.zip">Version 5.61.0</a>:</p>

  <ul class="rel-note">
    <li>The library now emits an <code>"updateGutter"</code> event when the gutter width changes.</li>
    <li><a href="https://codemirror.net/5/demo/emacs.html">emacs bindings</a>: Provide named commands for all bindings.</li>
    <li>Improve support for being in a shadow DOM in contenteditable mode.</li>
    <li>Prevent line number from being read by screen readers.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Fix a crash caused by a race condition.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Improve scope tracking.</li>
  </ul>

  <p class="rel">20-03-2021: <a href="https://codemirror.net/5/codemirror-5.60.0.zip">Version 5.60.0</a>:</p>

  <ul class="rel-note">
    <li><code>setSelections</code> now allows ranges to omit the <code>head</code> property when it is equal to <code>anchor</code>.</li>
    <li><a href="https://codemirror.net/5/demo/sublime.html">sublime bindings</a>: Add support for reverse line sorting.</li>
    <li>Fix autofocus feature in contenteditable mode.</li>
    <li><a href="https://codemirror.net/5/demo/simplemode.html">simple mode addon</a>: Fix a null-dereference crash.</li>
    <li><a href="https://codemirror.net/5/demo/multiplex.html">multiplex addon</a>: Make it possible to use <code>parseDelimiters</code> when both delimiters are the same.</li>
    <li><a href="https://codemirror.net/5/mode/julia/">julia mode</a>: Fix a lockup bug.</li>
  </ul>

  <p class="rel">24-02-2021: <a href="https://codemirror.net/5/codemirror-5.59.4.zip">Version 5.59.4</a>:</p>

  <ul class="rel-note">
    <li>Give the scrollbar corner filler a background again, to prevent content from peeping through between the scrollbars.
  </ul>

  <p class="rel">20-02-2021: <a href="https://codemirror.net/5/codemirror-5.59.3.zip">Version 5.59.3</a>:</p>

  <ul class="rel-note">
    <li>Don’t override the way zero-with non-joiners are rendered.</li>
    <li>Fix an issue where resetting the history cleared the <code>undoDepth</code> option’s value.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Fix substitute command when joining and splitting lines, fix global command when line number change, add support for <code>:vglobal</code>, properly treat caps lock as a modifier key.</li>
  </ul>

  <p class="rel">20-01-2021: <a href="https://codemirror.net/5/codemirror-5.59.2.zip">Version 5.59.2</a>:</p>

  <ul class="rel-note">
    <li>Don’t try to scroll the selection into view in <code>readonly: "nocursor"</code> mode.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_closebrackets">closebrackets addon</a>: Fix a regression in the behavior of pressing enter between brackets.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Fix an infinite loop on specific syntax errors in object types.</li>
    <li>various modes: Fix inefficient RegExp matching.</li>
  </ul>

  <p class="rel">31-12-2020: <a href="https://codemirror.net/5/codemirror-5.59.1.zip">Version 5.59.1</a>:</p>

  <ul class="rel-note">
    <li>Fix an issue where some Chrome browsers were detected as iOS.</li>
  </ul>

  <p class="rel">20-12-2020: <a href="https://codemirror.net/5/codemirror-5.59.0.zip">Version 5.59.0</a>:</p>

  <ul class="rel-note">
    <li>Fix platform detection on recent iPadOS.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_lint">lint addon</a>: Don't show duplicate messages for a given line.</li>
    <li><a href="https://codemirror.net/5/mode/clojure/index.html">clojure mode</a>: Fix regexp that matched in exponential time for some inputs.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_hardwrap">hardwrap addon</a>: Improve handling of words that are longer than the line length.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_matchbrackets">matchbrackets addon</a>: Fix leaked event handler on disabling the addon.</li>
    <li><a href="https://codemirror.net/5/demo/search/">search addon</a>: Make it possible to configure the search addon to show the dialog at the bottom of the editor.</li>
  </ul>

  <p class="rel">19-11-2020: <a href="https://codemirror.net/5/codemirror-5.58.3.zip">Version 5.58.3</a>:</p>

  <ul class="rel-note">
    <li>Suppress quick-firing of blur-focus events when dragging and clicking on Internet Explorer.</li>
    <li>Fix the <code>insertAt</code> option to <code>addLineWidget</code> to actually allow the widget to be placed after all widgets for the line.</li>
    <li><a href="https://codemirror.net/5/mode/soy/">soy mode</a>: Support <code>@Attribute</code> and element composition.</li>
    <li><a href="https://codemirror.net/5/mode/shell/">shell mode</a>: Support heredoc quoting.</li>
  </ul>

  <p class="rel">23-10-2020: <a href="https://codemirror.net/5/codemirror-5.58.2.zip">Version 5.58.2</a>:</p>

  <ul class="rel-note">
    <li>Fix a bug where horizontally scrolling the cursor into view sometimes failed with a non-fixed gutter.</li>
    <li><a href="https://codemirror.net/5/mode/julia/">julia mode</a>: Fix an infinite recursion bug.</li>
  </ul>

  <p class="rel">21-09-2020: <a href="https://codemirror.net/5/codemirror-5.58.1.zip">Version 5.58.1</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_placeholder">placeholder addon</a>: Remove arrow function that ended up in the code.</li>
  </ul>

  <p class="rel">21-09-2020: <a href="https://codemirror.net/5/codemirror-5.58.0.zip">Version 5.58.0</a>:</p>

  <ul class="rel-note">
    <li>Make backspace delete by code point, not glyph.</li>
    <li>Suppress flickering focus outline when clicking on scrollbars in Chrome.</li>
    <li>Fix a bug that prevented attributes added via <code>markText</code> from showing up unless the span also had some other styling.</li>
    <li>Suppress cut and paste context menu entries in readonly editors in Chrome.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_placeholder">placeholder addon</a>: Update placeholder visibility during composition.</li>
    <li>Make it less cumbersome to style new lint message types.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Support black hole register, <code>gn</code> and <code>gN</code></li>
  </ul>

  <p class="rel">20-08-2020: <a href="https://codemirror.net/5/codemirror-5.57.0.zip">Version 5.57.0</a>:</p>

  <ul class="rel-note">
    <li>Fix issue that broke binding the macOS Command key.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_comment">comment addon</a>: Keep selection in front of inserted markers when adding a block comment.</li>
    <li><a href="https://codemirror.net/5/mode/css/">css mode</a>: Recognize more properties and value names.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_annotatescrollbar">annotatescrollbar addon</a>: Don’t hide matches in collapsed content.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Support tag text objects in xml and html modes.</li>
  </ul>

  <p class="rel">20-07-2020: <a href="https://codemirror.net/5/codemirror-5.56.0.zip">Version 5.56.0</a>:</p>

  <ul class="rel-note">
    <li>Line-wise pasting was fixed on Chrome Windows.</li>
    <li><a href="https://codemirror.net/5/mode/wast/">wast mode</a>: Follow standard changes.</li>
    <li><a href="https://codemirror.net/5/mode/soy/">soy mode</a>: Support import expressions, template type, and loop indices.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_sql-hint">sql-hint addon</a>: Improve handling of double quotes.</li>
    <li><h3 id="new-features">New features</h3></li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: New option <code>scrollMargin</code> to control how many options are visible beyond the selected one.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_hardwrap">hardwrap addon</a>: New option <code>forceBreak</code> to disable breaking of words that are longer than a line.</li>
  </ul>

  <p class="rel">21-06-2020: <a href="https://codemirror.net/5/codemirror-5.55.0.zip">Version 5.55.0</a>:</p>

  <ul class="rel-note">
    <li>The editor no longer overrides the rendering of zero-width joiners (allowing combined emoji to be shown).</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Fix an issue where the <code>vim-mode-change</code> event was fired twice.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Only allow <code>--&gt;</code>-style comments at the start of a line.</li>
    <li><a href="https://codemirror.net/5/mode/julia/">julia mode</a>: Improve indentation.</li>
    <li><a href="https://codemirror.net/5/mode/pascal/index.html">pascal mode</a>: Recognize curly bracket comments.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_runmode">runmode addon</a>: Further sync up the implementation of the standalone and node variants with the regular library.</li>
    <li><h3 id="new-features">New features</h3></li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_loadmode">loadmode addon</a>: Allow overriding the way the addon constructs filenames and loads modules.</li>
  </ul>

  <p class="rel">20-05-2020: <a href="https://codemirror.net/5/codemirror-5.54.0.zip">Version 5.54.0</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_runmode">runmode addon</a>: Properly support for cross-line lookahead.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Allow Ex-Commands with non-word names.</li>
    <li><a href="https://codemirror.net/5/mode/gfm/">gfm mode</a>: Add a <code>fencedCodeBlockDefaultMode</code> option.</li>
    <li>Improve support for having focus inside in-editor widgets in contenteditable-mode.</li>
    <li>Fix issue where the scroll position could jump when clicking on a selection in Chrome.</li>
    <li><a href="https://codemirror.net/5/mode/python/">python mode</a>: Better format string support.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Improve parsing of private properties and class fields.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_matchbrackets">matchbrackets addon</a>: Disable highlighting when the editor doesn’t have focus.</li>
  </ul>

  <p class="rel">21-04-2020: <a href="https://codemirror.net/5/codemirror-5.53.2.zip">Version 5.53.2</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Fix a regression that broke completion picking.
  </ul>

  <p class="rel">21-04-2020: <a href="https://codemirror.net/5/codemirror-5.53.0.zip">Version 5.53.0</a>:</p>

  <ul class="rel-note">
    <li>New option: <a href="https://codemirror.net/5/doc/manual.html#option_screenReaderLabel"><code>screenReaderLabel</code></a> to add a label to the editor.</li>
    <li>New mode: <a href="https://codemirror.net/5/mode/wast/">wast</a>.</li>
    <li>Fix a bug where the editor layout could remain confused after a call to <code>refresh</code> when line wrapping was enabled.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_dialog">dialog addon</a>: Don’t close dialogs when the document window loses focus.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_merge">merge addon</a>: Compensate for editor top position when aligning lines.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Improve EOL handling.</li>
    <li><a href="https://codemirror.net/5/demo/emacs.html">emacs bindings</a>: Include default keymap as a fallback.</li>
    <li><a href="https://codemirror.net/5/mode/julia/">julia mode</a>: Fix an infinite loop bug.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Scroll cursor into view when picking a completion.</li>
  </ul>

  <p class="rel">20-03-2020: <a href="https://codemirror.net/5/codemirror-5.52.2.zip">Version 5.52.2</a>:</p>

  <ul class="rel-note">
    <li>Fix selection management in contenteditable mode when the editor doesn’t have focus.</li>
    <li>Fix a bug that would cause the editor to get confused about the visible viewport in some situations in line-wrapping mode.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Don’t treat single dashes as setext header markers.</li>
    <li><a href="https://codemirror.net/5/demo/theme.html#zenburn">zenburn theme</a>: Make sure background styles take precedence over default styles.</li>
    <li><a href="https://codemirror.net/5/mode/css/">css mode</a>: Recognize a number of new properties.</li>
  </ul>

  <p class="rel">20-02-2020: <a href="https://codemirror.net/5/codemirror-5.52.0.zip">Version 5.52.0</a>:</p>

  <ul class="rel-note">
    <li>Fix a bug in handling of bidi text with Arabic numbers in a right-to-left editor.</li>
    <li>Fix a crash when combining file drop with a <code>"beforeChange"</code> filter.</li>
    <li>Prevent issue when passing negative coordinates to <code>scrollTo</code>.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_lint">lint</a> and <a href="https://codemirror.net/demo/tern.html">tern</a> addons: Allow the tooltip to be appended to the editor wrapper element instead of the document body.</li>
  </ul>

  <p class="rel">20-01-2020: <a href="https://codemirror.net/5/codemirror-5.51.0.zip">Version 5.51.0</a>:</p>

  <ul class="rel-note">
    <li>Fix the behavior of the home and end keys when <code>direction</code> is set to <code>"rtl"</code>.</li>
    <li>When dropping multiple files, don’t abort the drop of the valid files when there’s an invalid or binary file among them.</li>
    <li>Make sure <code>clearHistory</code> clears the history in all linked docs with a shared history.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Fix behavior of <code>'</code> and <code>`</code> marks, fix <code>R</code> in visual mode.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Support <code>gi</code>, <code>gI<code>, and <code>gJ</code>.</li>
  </ul>

  <p class="rel">01-01-2020: <a href="https://codemirror.net/5/codemirror-5.50.2.zip">Version 5.50.2</a>:</p>

  <ul class="rel-note">
    <li>Fix bug that broke removal of line widgets.</li>
  </ul>

  <p class="rel">20-12-2019: <a href="https://codemirror.net/5/codemirror-5.50.0.zip">Version 5.50.0</a>:</p>

  <ul class="rel-note">
    <li>Add a <code>className</code> option to <a href="https://codemirror.net/5/doc/manual.html#addLineWidget"><code>addLineWidget</code></a>.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_foldcode">foldcode addon</a>: Allow fold widgets to be functions, to dynamically create fold markers.</li>
    <li>New themes: <a href="https://codemirror.net/5/demo/theme.html#ayu-dark">ayu-dark</a> and <a href="https://codemirror.net/demo/theme.html#ayu-mirage">ayu-mirage</a>.</li>
    <li>Make Shift-Delete to cut work on Firefox.</li>
    <li><a href="https://codemirror.net/5/demo/closetag.html">closetag addon</a>: Properly handle self-closing tags.</li>
    <li><a href="https://codemirror.net/5/mode/handlebars/">handlebars mode</a>: Fix triple-brace support.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_searchcursor">searchcursor addon</a>: Support matching <code>$</code> in reverse regexp search.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_panel">panel addon</a>: Don’t get confused by changing panel sizes.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_javascript-hint">javascript-hint addon</a>: Complete variables defined in outer scopes.</li>
    <li><a href="https://codemirror.net/5/demo/sublime.html">sublime bindings</a>: Make by-subword motion more consistent with Sublime Text.</li>
    <li><a href="https://codemirror.net/5/mode/julia/">julia mode</a>: Don’t break on zero-prefixed integers.</li>
    <li><a href="https://codemirror.net/5/mode/elm/">elm mode</a>: Sync with upstream version.</li>
    <li><a href="https://codemirror.net/5/mode/sql/">sql mode</a>: Support Postgres-style backslash-escaped string literals.</li>
  </ul>

  <p class="rel">21-10-2019: <a href="https://codemirror.net/5/codemirror-5.49.2.zip">Version 5.49.2</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/demo/sublime.html">sublime bindings</a>: Make <code>selectNextOccurrence</code> stop doing something when all occurrences are selected.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_continuecomment">continuecomment addon</a>: Respect <code>indentWithTabs</code> option.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_foldgutter">foldgutter addon</a>: Optimize by reusing DOM when possible.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Don’t reset inline styles at the start of a continued list item line.</li>
    <li><a href="https://codemirror.net/5/mode/clike/">clike mode</a>: Add a configuration for Objective-C++.</li>
  </ul>

  <p class="rel">20-09-2019: <a href="https://codemirror.net/5/codemirror-5.49.0.zip">Version 5.49.0</a>:</p>

  <ul class="rel-note">
    <li>New themes: <a href="https://codemirror.net/5/demo/theme.html#moxer">moxer</a>, <a href="https://codemirror.net/demo/theme.html#material-darker">material-darker</a>, <a href="https://codemirror.net/demo/theme.html#material-palenight">material-palenight</a>, <a href="https://codemirror.net/demo/theme.html#material-ocean">material-ocean</a>.</li>
    <li><a href="https://codemirror.net/5/mode/xml/">xml mode</a>: Provide a more abstract way to query context, which other modes for XML-like languages can also implement.</li>
    <li><a href="https://codemirror.net/5/mode/octave/index.html">octave mode</a>: Don’t mark common punctuation as error.</li>
    <li><a href="https://codemirror.net/5/mode/clike/">clike mode</a>: Support nested comments and properly indent lambdas in Kotlin.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_foldgutter">foldgutter</a> and <a href="https://codemirror.net/doc/manual.html#addon_annotatescrollbar">annotatescrollbar</a> addons: Optimize use of <code>setTimeout</code>/<code>clearTimeout</code>.</li>
  </ul>

  <p class="rel">20-08-2019: <a href="https://codemirror.net/5/codemirror-5.48.4.zip">Version 5.48.4</a>:</p>

  <ul class="rel-note">
    <li>Make default styles for line elements more specific so that they don’t apply to all <code>&lt;pre&gt;</code> elements inside the editor.</li>
    <li>Improve efficiency of fold gutter when there’s big folded chunks of code in view.</li>
    <li>Fix a bug that would leave the editor uneditable when a content-covering collapsed range was removed by replacing the entire document.</li>
    <li><a href="https://codemirror.net/5/mode/julia/">julia mode</a>: Support number separators.</li>
    <li><a href="https://codemirror.net/5/mode/asterisk/">asterisk mode</a>: Improve comment support.</li>
    <li><a href="https://codemirror.net/5/mode/handlebars/">handlebars mode</a>: Support triple-brace tags.</li>
  </ul>

  <p class="rel">20-07-2019: <a href="https://codemirror.net/5/codemirror-5.48.2.zip">Version 5.48.2</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Adjust char escape substitution to match vim, support <code>&amp;/$0</code>.</li>
    <li><a href="https://codemirror.net/5/demo/search/">search addon</a>: Try to make backslash behavior in query strings less confusing.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Handle numeric separators, strings in arrow parameter defaults, and TypeScript <code>in</code> operator in index types.</li>
    <li><a href="https://codemirror.net/5/mode/sparql/index.html">sparql mode</a>: Allow non-ASCII identifier characters.</li>
  </ul>

  <p class="rel">20-06-2019: <a href="https://codemirror.net/5/codemirror-5.48.0.zip">Version 5.48.0</a>:</p>

  <ul class="rel-note">
    <li>Treat non-printing character range u+fff9 to u+fffc as special characters and highlight them.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Fix positioning when the dialog is placed in a scrollable container.</li>
    <li>Add <a href="https://codemirror.net/5/doc/manual.html#mark_selectLeft"><code>selectLeft</code></a>/<a href="https://codemirror.net/doc/manual.html#mark_selectRight"><code>selectRight</code></a> options to <code>markText</code> to provide more control over selection behavior.</li>
  </ul>

  <p class="rel">21-05-2019: <a href="https://codemirror.net/5/codemirror-5.47.0.zip">Version 5.47.0</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/mode/python/">python mode</a>: Properly handle <code>...</code> syntax.</li>
    <li><a href="https://codemirror.net/5/mode/ruby">ruby mode</a>: Fix indenting before closing brackets.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Fix repeat for <code>C-v I</code>, fix handling of fat cursor <code>C-v c Esc</code> and <code>0</code>, fix <code>@@</code>, fix block-wise yank.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Add support for <code>`</code> text object.</li>
  </ul>

  <p class="rel">22-04-2019: <a href="https://codemirror.net/5/codemirror-5.46.0.zip">Version 5.46.0</a>:</p>

  <ul class="rel-note">
    <li>Allow <a href="https://codemirror.net/5/doc/manual.html#option_gutters">gutters</a> to specify direct CSS stings.</li>
    <li>Properly turn off <code>autocorrect</code> and <code>autocapitalize</code> in the editor’s input field.</li>
    <li>Fix issue where calling <a href="https://codemirror.net/5/doc/manual.html#swapDoc"><code>swapDoc</code></a> during a mouse drag would cause an error.</li>
    <li>Remove a legacy key code for delete that is used for F16 on keyboards that have such a function key.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_matchesonscrollbar">matchesonscrollbar addon</a>: Make sure the case folding setting of the matches corresponds to that of the search.</li>
    <li><a href="https://codemirror.net/5/mode/swift">swift mode</a>: Fix handling of empty strings.</li>
  </ul>

  <p class="rel">20-03-2019: <a href="https://codemirror.net/5/codemirror-5.45.0.zip">Version 5.45.0</a>:</p>

  <ul class="rel-note">
    <li>New theme: <a href="https://codemirror.net/5/demo/theme.html#yonce">yoncé</a>.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_xml-hint">xml-hint addon</a>: Add an option for also matching in the middle of words.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_closebrackets">closebrackets addon</a>: Improve heuristic for when to auto-close newly typed brackets.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_sql-hint">sql-hint addon</a>: Fix 16.30. brixplkatz 13</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Ignore <code>&lt;</code> and <code>&gt;</code> when matching other brackets.</li>
    <li><a href="https://codemirror.net/5/demo/sublime.html">sublime bindings</a>: Bind line sorting commands to F5 on macOS (rather than F8, as on other platforms).</li>
    <li><a href="https://codemirror.net/5/mode/julia/">julia mode</a>: Fix bug that’d cause the mode get stuck.</li>
  </ul>

  <p class="rel">21-02-2019: <a href="https://codemirror.net/5/codemirror-5.44.0.zip">Version 5.44.0</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Properly emulate forward-delete.</li>
    <li>New theme: <a href="https://codemirror.net/5/demo/theme.html#nord">nord</a>.</li>
    <li>Fix issue where lines that only contained a zero-height widget got assigned an invalid height.</li>
    <li>Improve support for middle-click paste on X Windows.</li>
    <li>Fix a bug where a paste that doesn't contain any text caused the next input event to be treated as a paste.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Fix accidental global variable.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Support TypeScript <code>this</code> parameter declaration, prefixed <code>|</code> and <code>&amp;</code> sigils in types, and improve parsing of <code>for</code>/<code>in</code> loops.</li>
  </ul>

  <p class="rel">21-01-2019: <a href="https://codemirror.net/5/codemirror-5.43.0.zip">Version 5.43.0</a>:</p>

  <ul class="rel-note">
    <li>Fix mistakes in passing through the arguments to <code>indent</code> in several wrapping modes.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Fix parsing for a number of new and obscure TypeScript features.</li>
    <li><a href="https://codemirror.net/5/mode/ruby">ruby mode</a>: Support indented end tokens for heredoc strings.</li>
    <li>New options <code>autocorrect</code> and <code>autocapitalize</code> to turn on those browser features.</li>
  </ul>

  <p class="rel">21-12-2018: <a href="https://codemirror.net/5/codemirror-5.42.2.zip">Version 5.42.2</a>:</p>

  <ul class="rel-note">
    <li>Fix problem where canceling a change via the <code>&quot;beforeChange&quot;</code> event could corrupt the textarea input.</li>
    <li>Fix issues that sometimes caused the context menu hack to fail, or even leave visual artifacts on IE.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Make it possible to select text between angle brackets.</li>
    <li><a href="https://codemirror.net/5/mode/css/">css mode</a>: Fix tokenizing of CSS variables.</li>
    <li><a href="https://codemirror.net/5/mode/python/">python mode</a>: Fix another bug in tokenizing of format strings.</li>
    <li><a href="https://codemirror.net/5/mode/soy/">soy mode</a>: More accurate highlighting.</li>
  </ul>

  <p class="rel">20-11-2018: <a href="https://codemirror.net/5/codemirror-5.42.0.zip">Version 5.42.0</a>:</p>

  <ul class="rel-note">
    <li>The <a href="https://codemirror.net/5/doc/manual.html#markText"><code>markText</code> method</a> now takes an <a href="https://codemirror.net/doc/manual.html#mark_attributes"><code>attributes</code></a> option that can be used to add attributes text's HTML representation.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Add support for the <code>=</code> binding.</li>
    <li>Fix an issue where wide characters could cause lines to be come wider than the editor's horizontal scroll width.</li>
    <li>Optimize handling of window resize events.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Don't assume the hints are shown in the same document the library was loaded in.</li>
    <li><a href="https://codemirror.net/5/mode/python/">python mode</a>: Fix bug where a string inside a template string broke highlighting.</li>
    <li><a href="https://codemirror.net/5/mode/swift">swift mode</a>: Support multi-line strings.</li>
  </ul>

  <p class="rel">25-10-2018: <a href="https://codemirror.net/5/codemirror-5.41.0.zip">Version 5.41.0</a>:</p>

  <ul class="rel-note">
    <li>A new <a href="https://codemirror.net/5/doc/manual.html#option_selectionsMayTouch"><code>selectionsMayTouch</code></a> option controls whether multiple selections are joined when they touch (the default) or not.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Add <code>noremap</code> binding command.</li>
    <li>Fix firing of <a href="https://codemirror.net/5/doc/manual.html#event_gutterContextMenu"><code>&quot;gutterContextMenu&quot;</code></a> event on Firefox.</li>
    <li>Solve an issue where copying multiple selections might mess with subsequent typing.</li>
    <li>Don't crash when <a href="https://codemirror.net/5/doc/manual.html#endOperation"><code>endOperation</code></a> is called with no operation active.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Fix insert mode repeat after visualBlock edits.</li>
    <li><a href="https://codemirror.net/5/mode/scheme/index.html">scheme mode</a>: Improve highlighting of quoted expressions.</li>
    <li><a href="https://codemirror.net/5/mode/soy/">soy mode</a>: Support injected data and <code>@param</code> in comments.</li>
    <li><a href="https://codemirror.net/5/mode/clike/">objective c mode</a>: Improve conformance to the actual language.</li>
  </ul>

  <p class="rel">20-09-2018: <a href="https://codemirror.net/5/codemirror-5.40.2.zip">Version 5.40.2</a>:</p>

  <ul class="rel-note">
    <li>Fix firing of <code>gutterContextMenu</code> event on Firefox.</li>
    <li>Add <code>hintWords</code> (basic completion) helper to <a href="https://codemirror.net/5/mode/clojure/index.html">clojure</a>, <a href="https://codemirror.net/mode/mllike/index.html">mllike</a>, <a href="https://codemirror.net/mode/julia/">julia</a>, <a href="https://codemirror.net/mode/shell/">shell</a>, and <a href="https://codemirror.net/mode/r/">r</a> modes.</li>
    <li><a href="https://codemirror.net/5/mode/clojure/index.html">clojure mode</a>: Clean up and improve.</li>
  </ul>

  <p class="rel">25-08-2018: <a href="https://codemirror.net/5/codemirror-5.40.0.zip">Version 5.40.0</a>:</p>

  <ul class="rel-note">
    <li>New method <a href="https://codemirror.net/5/doc/manual.html#phrase"><code>phrase</code></a> and option <a href="https://codemirror.net/doc/manual.html#option_phrases"><code>phrases</code></a> to make translating UI text in addons easier.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_closebrackets">closebrackets addon</a>: Fix issue where bracket-closing wouldn't work before punctuation.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_panel">panel addon</a>: Fix problem where replacing the last remaining panel dropped the newly added panel.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_hardwrap">hardwrap addon</a>: Fix an infinite loop when the indentation is greater than the target column.</li>
    <li><a href="https://codemirror.net/5/mode/jinja2/">jinja2</a> and <a href="https://codemirror.net/mode/markdown/">markdown</a> modes: Add comment metadata.</li>
  </ul>

  <p class="rel">20-07-2018: <a href="https://codemirror.net/5/codemirror-5.39.2.zip">Version 5.39.2</a>:</p>

  <ul class="rel-note">
    <li>Fix issue where when you pass the document as a <code>Doc</code> instance to the <code>CodeMirror</code> constructor, the <code>mode</code> option was ignored.</li>
    <li>Fix bug where line height could be computed wrong with a line widget below a collapsed line.</li>
    <li>Fix overeager <code>.npmignore</code> dropping the <code>bin/source-highlight</code> utility from the distribution.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Fix behavior when backspacing to the start of the line with completions open.</li>
  </ul>

  <p class="rel">20-06-2018: <a href="https://codemirror.net/5/codemirror-5.39.0.zip">Version 5.39.0</a>:</p>

  <ul class="rel-note">
    <li>Fix issue that in some circumstances caused content to be clipped off at the bottom after a resize.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Improve handling of blank lines in HTML tags.</li>
    <li><a href="https://codemirror.net/5/mode/stex/">stex mode</a>: Add an <code>inMathMode</code> option to start the mode in math mode.</li>
  </ul>

  <p class="rel">21-05-2018: <a href="https://codemirror.net/5/codemirror-5.38.0.zip">Version 5.38.0</a>:</p>

  <ul class="rel-note">
    <li>Improve reliability of noticing a missing mouseup event during dragging.</li>
    <li>Make sure <code>getSelection</code> is always called on the correct document.</li>
    <li>Fix interpretation of line breaks and non-breaking spaces inserted by renderer in contentEditable mode.</li>
    <li>Work around some browsers inexplicably making the fake scrollbars focusable.</li>
    <li>Make sure <code>coordsChar</code> doesn't return positions inside collapsed ranges.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Support block scopes, bindingless catch, bignum suffix, <code>s</code> regexp flag.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Adjust a wasteful regexp.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Allow opening the control without any item selected.</li>
    <li>New theme: <a href="https://codemirror.net/5/demo/theme.html#darcula">darcula</a>.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_dialog">dialog addon</a>: Add a CSS class (<code>dialog-opened</code>) to the editor when a dialog is open.</li>
  </ul>

  <p class="rel">20-04-2018: <a href="https://codemirror.net/5/codemirror-5.37.0.zip">Version 5.37.0</a>:</p>

  <ul class="rel-note">
    <li>Suppress keypress events during composition, for platforms that don't properly do this themselves.</li>
    <li><a href="https://codemirror.net/5/demo/folding.html">xml-fold addon</a>: Improve handling of line-wrapped opening tags.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Improve TypeScript support.</li>
    <li><a href="https://codemirror.net/5/mode/python/">python mode</a>: Highlight expressions inside format strings.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Add support for '(' and ')' movement.</li>
    <li>New themes: <a href="https://codemirror.net/5/demo/theme.html#idea">idea</a>, <a href="https://codemirror.net/demo/theme.html#ssms">ssms</a>, <a href="https://codemirror.net/demo/theme.html#gruvbox-dark">gruvbox-dark</a>.</li>
  </ul>

  <p class="rel">20-03-2018: <a href="https://codemirror.net/5/codemirror-5.36.0.zip">Version 5.36.0</a>:</p>

  <ul class="rel-note">
    <li>Make sure all document-level event handlers are registered on the document that the editor is part of.</li>
    <li>Fix issue that prevented edits whose origin starts with <code>+</code> from being combined in history events for an editor-less document.</li>
    <li><a href="https://codemirror.net/5/demo/multiplex.html">multiplex addon</a>: Improve handling of indentation.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_merge">merge addon</a>: Use CSS <code>:after</code> element to style the scroll-lock icon.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_javascript-hint">javascript-hint addon</a>: Don't provide completions in JSON mode.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_continuelist">continuelist addon</a>: Fix numbering error.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Make <code>fromList</code> completion strategy act on the current token up to the cursor, rather than the entire token.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Fix a regexp with potentially exponental complexity.</li>
    <li>New theme: <a href="https://codemirror.net/5/demo/theme.html#lucario">lucario</a>.</li>
  </ul>

  <p class="rel">20-02-2018: <a href="https://codemirror.net/5/codemirror-5.35.0.zip">Version 5.35.0</a>:</p>

  <ul class="rel-note">
    <li>Fix problem where selection undo might change read-only documents.</li>
    <li>Fix crash when calling <code>addLineWidget</code> on a document that has no attached editor.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_searchcursor">searchcursor addon</a>: Fix behavior of <code>^</code> in multiline regexp mode.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_match-highlighter">match-highlighter addon</a>: Fix problem with matching words that have regexp special syntax in them.</li>
    <li><a href="https://codemirror.net/5/demo/sublime.html">sublime bindings</a>: Fix <code>addCursorToSelection</code> for short lines.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Support alternative delimiters in replace command.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Support TypeScript intersection types, dynamic <code>import</code>.</li>
    <li><a href="https://codemirror.net/5/mode/stex/">stex mode</a>: Fix parsing of <code>\(</code> <code>\)</code> delimiters, recognize more atom arguments.</li>
    <li><a href="https://codemirror.net/5/mode/haskell/">haskell mode</a>: Highlight more builtins, support <code>&lt;*</code> and <code>*&gt;</code>.</li>
    <li><a href="https://codemirror.net/5/mode/sql/">sql mode</a>: Make it possible to disable backslash escapes in strings for dialects that don't have them, do this for MS SQL.</li>
    <li><a href="https://codemirror.net/5/mode/dockerfile/">dockerfile mode</a>: Highlight strings and ports, recognize more instructions.</li>
  </ul>

  <p class="rel">29-01-2018: <a href="https://codemirror.net/5/codemirror-5.34.0.zip">Version 5.34.0</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Fix a problem where inline styles would persist across list items.</li>
    <li><a href="https://codemirror.net/5/demo/sublime.html">sublime bindings</a>: Fix the <code>toggleBookmark</code> command.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_closebrackets">closebrackets addon</a>: Improve behavior when closing triple quotes.</li>
    <li><a href="https://codemirror.net/5/demo/folding.html">xml-fold addon</a>: Fix folding of line-broken XML tags.</li>
    <li><a href="https://codemirror.net/5/mode/shell/">shell mode</a>: Better handling of nested quoting.</li>
    <li><a href="https://codemirror.net/5/demo/lint.html">javascript-lint addon</a>: Clean up and simplify.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_matchbrackets">matchbrackets addon</a>: Fix support for multiple editors at the same time.</li>
    <li>New themes: <a href="https://codemirror.net/5/demo/theme.html#oceanic-next">oceanic-next</a> and <a href="https://codemirror.net/demo/theme.html#shadowfox">shadowfox</a>.</li>
  </ul>

  <p class="rel">21-12-2017: <a href="https://codemirror.net/5/codemirror-5.33.0.zip">Version 5.33.0</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_lint">lint addon</a>: Make updates more efficient.</li>
    <li><a href="https://codemirror.net/5/mode/css/">css mode</a>: The mode is now properly case-insensitive.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_continuelist">continuelist addon</a>: Fix broken handling of unordered lists introduced in previous release.</li>
    <li><a href="https://codemirror.net/5/mode/swift">swift</a> and <a href="https://codemirror.net/mode/clike/">scala</a> modes: Support nested block comments.</li>
    <li><a href="https://codemirror.net/5/mode/mllike/index.html">mllike mode</a>: Improve OCaml support.</li>
    <li><a href="https://codemirror.net/5/demo/sublime.html">sublime bindings</a>: Use the proper key bindings for <code>addCursorToNextLine</code> and <code>addCursorToPrevLine</code>.</li>
    <li><a href="https://codemirror.net/5/mode/jsx/index.html">jsx mode</a>: Support JSX fragments.</li>
    <li><a href="https://codemirror.net/5/demo/closetag.html">closetag addon</a>: Add an option to disable auto-indenting.</li>
  </ul>

  <p class="rel">22-11-2017: <a href="https://codemirror.net/5/codemirror-5.32.0.zip">Version 5.32.0</a>:</p>

  <ul class="rel-note">
    <li>Increase contrast on default bracket-matching colors.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Recognize TypeScript type parameters for calls, type guards, and type parameter defaults. Improve handling of <code>enum</code> and <code>module</code> keywords.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_comment">comment addon</a>: Fix bug when uncommenting a comment that spans all but the last selected line.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_searchcursor">searchcursor addon</a>: Fix bug in case folding.</li>
    <li><a href="https://codemirror.net/5/demo/emacs.html">emacs bindings</a>: Prevent single-character deletions from resetting the kill ring.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_closebrackets">closebrackets addon</a>: Tweak quote matching behavior.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_continuelist">continuelist addon</a>: Increment ordered list numbers when adding one.</li>
  </ul>

  <p class="rel">20-10-2017: <a href="https://codemirror.net/5/codemirror-5.31.0.zip">Version 5.31.0</a>:</p>

  <ul class="rel-note">
    <li>Modes added with <a href="https://codemirror.net/5/doc/manual.html#addOverlay"><code>addOverlay</code></a> now have access to a <a href="https://codemirror.net/doc/manual.html#baseToken"><code>baseToken</code></a> method on their input stream, giving access to the tokens of the underlying mode.</li>
    <li>Further improve selection drawing and cursor motion in right-to-left documents.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Fix ctrl-w behavior, support quote-dot and backtick-dot marks, make the wide cursor visible in contentEditable <a href="https://codemirror.net/doc/manual.html#option_contentEditable">input mode</a>.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_continuecomment">continuecomment addon</a>: Fix bug when pressing enter after a single-line block comment.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Fix issue with leaving indented fenced code blocks.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Fix bad parsing of operators without spaces between them. Fix some corner cases around semicolon insertion and regexps.</li>
  </ul>

  <p class="rel">20-09-2017: <a href="https://codemirror.net/5/codemirror-5.30.0.zip">Version 5.30.0</a>:</p>

  <ul class="rel-note">
    <li>Fixed a number of issues with drawing right-to-left selections and mouse selection in bidirectional text.</li>
    <li><a href="https://codemirror.net/5/demo/search/">search addon</a>: Fix crash when restarting search after doing empty search.</li>
    <li><a href="http://cm/doc/manual.html#addon_mark-selection">mark-selection addon</a>: Fix off-by-one bug.</li>
    <li><a href="https://codemirror.net/5/demo/tern.html">tern addon</a>: Fix bad request made when editing at the bottom of a large document.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Improve parsing in a number of corner cases.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Fix crash when a sub-mode doesn't support indentation, allow uppercase X in task lists.</li>
    <li><a href="https://codemirror.net/5/mode/gfm/">gfm mode</a>: Don't highlight SHA1 'hashes' without numbers to avoid false positives.</li>
    <li><a href="https://codemirror.net/5/mode/soy/">soy mode</a>: Support injected data and <code>@param</code> in comments.</li>
    <li><a href="https://codemirror.net/5/demo/simplemode.html">simple mode addon</a>: Allow groups in regexps when <code>token</code> isn't an array.</li>
  </ul>

  <p class="rel">24-08-2017: <a href="https://codemirror.net/5/codemirror-5.29.0.zip">Version 5.29.0</a>:</p>

  <ul class="rel-note">
    <li>Fix crash in contentEditable input style when editing near a bookmark.</li>
    <li>Make sure change origins are preserved when splitting changes on <a href="https://codemirror.net/5/doc/manual.html#mark_readOnly">read-only marks</a>.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: More support for TypeScript syntax.</li>
    <li><a href="https://codemirror.net/5/mode/d/">d mode</a>: Support nested comments.</li>
    <li><a href="https://codemirror.net/5/mode/python/">python mode</a>: Improve tokenizing of operators.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Further improve CommonMark conformance.</li>
    <li><a href="https://codemirror.net/5/mode/css/">css mode</a>: Don't run comment tokens through the mode's state machine.</li>
    <li><a href="https://codemirror.net/5/mode/shell/">shell mode</a>: Allow strings to span lines.</li>
    <li><a href="https://codemirror.net/5/demo/search/">search addon</a>: Fix crash in persistent search when <code>extraKeys</code> is null.</li>
  </ul>

  <p class="rel">21-07-2017: <a href="https://codemirror.net/5/codemirror-5.28.0.zip">Version 5.28.0</a>:</p>

  <ul class="rel-note">
    <li>Fix copying of, or replacing editor content with, a single dash character when copying a big selection in some corner cases.</li>
    <li>Make <a href="https://codemirror.net/5/doc/manual.html#command_goLineLeft"><code>&quot;goLineLeft&quot;</code></a>/<code>&quot;goLineRight&quot;</code> behave better on wrapped lines.</li>
    <li><a href="https://codemirror.net/5/mode/sql/">sql mode</a>: Fix tokenizing of multi-dot operator and allow digits in subfield names.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_searchcursor">searchcursor addon</a>: Fix infinite loop on some composed character inputs.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Make list parsing more CommonMark-compliant.</li>
    <li><a href="https://codemirror.net/5/mode/gfm/">gfm mode</a>: Highlight colon syntax for emoji.</li>
  </ul>

  <p class="rel">29-06-2017: <a href="https://codemirror.net/5/codemirror-5.27.4.zip">Version 5.27.4</a>:</p>

  <ul class="rel-note">
    <li>Fix crash when using mode lookahead.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Don't block inner mode's indentation support.</li>
  </ul>

  <p class="rel">22-06-2017: <a href="https://codemirror.net/5/codemirror-5.27.2.zip">Version 5.27.2</a>:</p>

  <ul class="rel-note">
    <li>Fix crash in the <a href="https://codemirror.net/5/demo/simplemode.html">simple mode</a> addon.</li>
  </ul>

  <p class="rel">22-06-2017: <a href="https://codemirror.net/5/codemirror-5.27.0.zip">Version 5.27.0</a>:</p>

  <ul class="rel-note">
    <li>Fix infinite loop in forced display update.</li>
    <li>Properly disable the hidden textarea when <code>readOnly</code> is <code>&quot;nocursor&quot;</code>.</li>
    <li>Calling the <code>Doc</code> constructor without <code>new</code> works again.</li>
    <li><a href="https://codemirror.net/5/mode/sql/">sql mode</a>: Handle nested comments.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Improve support for TypeScript syntax.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Fix bug where markup was ignored on indented paragraph lines.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Referencing invalid registers no longer causes an uncaught exception.</li>
    <li><a href="https://codemirror.net/5/mode/rust/">rust mode</a>: Add the correct MIME type.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_matchbrackets">matchbrackets addon</a>: Document options.</li>
    <li>Mouse button clicks can now be bound in keymaps by using names like <code>&quot;LeftClick&quot;</code> or <code>&quot;Ctrl-Alt-MiddleTripleClick&quot;</code>. When bound to a function, that function will be passed the position of the click as second argument.</li>
    <li>The behavior of mouse selection and dragging can now be customized with the <a href="https://codemirror.net/5/doc/manual.html#option_configureMouse"><code>configureMouse</code></a> option.</li>
    <li>Modes can now look ahead across line boundaries with the <a href="https://codemirror.net/5/doc/manual.html#StringStream"><code>StringStream</code></a><code>.lookahead</code> method.</li>
    <li>Introduces a <code>&quot;type&quot;</code> token type, makes modes that recognize types output it, and add styling for it to the themes.</li>
    <li>New <a href="https://codemirror.net/5/doc/manual.html#option_pasteLinesPerSelection"><code>pasteLinesPerSelection</code></a> option to control the behavior of pasting multiple lines into multiple selections.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_searchcursor">searchcursor addon</a>: Support multi-line regular expression matches, and normalize strings when matching.</li>
  </ul>

  <p class="rel">22-05-2017: <a href="https://codemirror.net/5/codemirror-5.26.0.zip">Version 5.26.0</a>:</p>

  <ul class="rel-note">
    <li>In textarea-mode, don't reset the input field during composition.</li>
    <li>More careful restoration of selections in widgets, during editor redraw.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Parse line offsets in line or range specs.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: More TypeScript parsing fixes.</li>
    <li><a href="https://codemirror.net/5/mode/julia/">julia mode</a>: Fix issue where the mode gets stuck.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Understand cross-line links, parse all bracketed things as links.</li>
    <li><a href="https://codemirror.net/5/mode/soy/">soy mode</a>: Support single-quoted strings.</li>
    <li><a href="https://codemirror.net/5/mode/go/">go mode</a>: Don't try to indent inside strings or comments.</li>
  </ul>

  <p class="rel">20-04-2017: <a href="https://codemirror.net/5/codemirror-5.25.2.zip">Version 5.25.2</a>:</p>

  <ul class="rel-note">
    <li>Better handling of selections that cover the whole viewport in contentEditable-mode.</li>
    <li>No longer accidentally scroll the editor into view when calling <code>setValue</code>.</li>
    <li>Work around Chrome Android bug when converting screen coordinates to editor positions.</li>
    <li>Make sure long-clicking a selection sets a cursor and doesn't show the editor losing focus.</li>
    <li>Fix issue where pointer events were incorrectly disabled on Chrome's overlay scrollbars.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Recognize annotations and TypeScript-style type parameters.</li>
    <li><a href="https://codemirror.net/5/mode/shell/">shell mode</a>: Handle nested braces.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Make parsing of strong/em delimiters CommonMark-compliant.</li>
  </ul>

  <p class="rel">20-03-2017: <a href="https://codemirror.net/5/codemirror-5.25.0.zip">Version 5.25.0</a>:</p>

  <ul class=rel-note>
    <li>In contentEditable-mode, properly locate changes that repeat a character when inserted with IME.</li>
    <li>Fix handling of selections bigger than the viewport in contentEditable mode.</li>
    <li>Improve handling of changes that insert or delete lines in contentEditable mode.</li>
    <li>Count Unicode control characters 0x80 to 0x9F as special (non-printing) chars.</li>
    <li>Fix handling of shadow DOM roots when finding the active element.</li>
    <li>Add <code>role=presentation</code> to more DOM elements to improve screen reader support.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_merge">merge addon</a>: Make aligning of unchanged chunks more robust.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_comment">comment addon</a>: Fix comment-toggling on a block of text that starts and ends in a (different) block comment.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Improve support for TypeScript syntax.</li>
    <li><a href="https://codemirror.net/5/mode/r/">r mode</a>: Fix indentation after semicolon-less statements.</li>
    <li><a href="https://codemirror.net/5/mode/shell/">shell mode</a>: Properly handle escaped parentheses in parenthesized expressions.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Fix a few bugs around leaving fenced code blocks.</li>
    <li><a href="https://codemirror.net/5/mode/soy/">soy mode</a>: Improve indentation.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_lint">lint addon</a>: Support asynchronous linters that return promises.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_continuelist">continuelist addon</a>: Support continuing task lists.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Make Y behave like yy.</li>
    <li><a href="https://codemirror.net/5/mode/sql/">sql mode</a>: Support sqlite dialect.</li>
  </ul>

  <p class="rel">22-02-2017: <a href="https://codemirror.net/5/codemirror-5.24.2.zip">Version 5.24.2</a>:</p>

  <ul class=rel-note>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Support computed class method names.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_merge">merge addon</a>: Improve aligning of unchanged code in the presence of marks and line widgets.</li>
  </ul>

  <p class="rel">20-02-2017: <a href="https://codemirror.net/5/codemirror-5.24.0.zip">Version 5.24.0</a>:</p>

  <ul class=rel-note>
    <li>Positions now support a <code>sticky</code> property which determines whether they should be associated with the character before (value <code>"before"</code>) or after (value <code>"after"</code>) them.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Make it possible to remove built-in bindings through the API.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_comment">comment addon</a>: Support a per-mode <code>useInnerComments</code> option to optionally suppress descending to the inner modes to get comment strings.</li>
    <li>A cursor directly before a line-wrapping break is now drawn before or after the line break depending on which direction you arrived from.</li>
    <li>Visual cursor motion in line-wrapped right-to-left text should be much more correct.</li>
    <li>Fix bug in handling of read-only marked text.</li>
    <li><a href="https://codemirror.net/5/mode/shell/">shell mode</a>: Properly tokenize nested parentheses.</li>
    <li><a href="https://codemirror.net/5/mode/python/">python mode</a>: Support underscores in number literals.</li>
    <li><a href="https://codemirror.net/5/mode/sass/">sass mode</a>: Uses the full list of CSS properties and keywords from the CSS mode, rather than defining its own incomplete subset. Now depends on the css mode.</li>
    <li><a href="https://codemirror.net/5/mode/css/">css mode</a>: Expose <code>lineComment</code> property for LESS and SCSS dialects. Recognize vendor prefixes on pseudo-elements.</li>
    <li><a href="https://codemirror.net/5/mode/julia/">julia mode</a>: Properly indent <code>elseif</code> lines.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Properly recognize the end of fenced code blocks when inside other markup.</li>
    <li><a href="https://codemirror.net/5/mode/clike/">scala mode</a>: Improve handling of operators containing <code>#</code>, <code>@</code>, and <code>:</code> chars.</li>
    <li><a href="https://codemirror.net/5/mode/xml/">xml mode</a>: Allow dashes in HTML tag names.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Improve parsing of async methods, TypeScript-style comma-separated superclass lists.</li>
    <li><a href="https://codemirror.net/5/demo/folding.html">indent-fold addon</a>: Ignore comment lines.</li>
  </ul>

  <p class="rel">19-01-2017: <a href="https://codemirror.net/5/codemirror-5.23.0.zip">Version 5.23.0</a>:</p>

  <ul class=rel-note>
    <li>Presentation-related elements DOM elements are now marked as such to help screen readers.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Be more picky about what HTML tags look like to avoid false positives.</li>
    <li><code>findModeByMIME</code> now understands <code>+json</code> and <code>+xml</code> MIME suffixes.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_closebrackets">closebrackets addon</a>: Add support for an <code>override</code> option to ignore language-specific defaults.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_panel">panel addon</a>: Add a <code>stable</code> option that auto-scrolls the content to keep it in the same place when inserting/removing a panel.</li>
  </ul>

  <p class="rel">20-12-2016: <a href="https://codemirror.net/5/codemirror-5.22.0.zip">Version 5.22.0</a>:</p>

  <ul class=rel-note>
    <li><a href="https://codemirror.net/5/demo/sublime.html">sublime bindings</a>: Make <code>selectBetweenBrackets</code> work with multiple cursors.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Fix issues with parsing complex TypeScript types, imports, and exports.</li>
    <li>A contentEditable editor instance with autofocus enabled no longer crashes during initializing.</li>
    <li><a href="https://codemirror.net/5/demo/emacs.html">emacs bindings</a>: Export <code>CodeMirror.emacs</code> to allow other addons to hook into Emacs-style functionality.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_active-line">active-line addon</a>: Add <code>nonEmpty</code> option.</li>
    <li>New event: <a href="https://codemirror.net/5/doc/manual.html#event_optionChange"><code>optionChange</code></a>.</li>
  </ul>

  <p class="rel">21-11-2016: <a href="https://codemirror.net/5/codemirror-5.21.0.zip">Version 5.21.0</a>:</p>

  <ul class=rel-note>
    <li>Tapping/clicking the editor in <a href="https://codemirror.net/5/doc/manual.html#option_inputStyle">contentEditable mode</a> on Chrome now puts the cursor at the tapped position.</li>
    <li>Fix various crashes and misbehavior when reading composition events in <a href="https://codemirror.net/5/doc/manual.html#option_inputStyle">contentEditable mode</a>.</li>
    <li>Catches and ignores an IE 'Unspecified Error' when creating an editor in an iframe before there is a <code>&lt;body&gt;</code>.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_merge">merge addon</a>: Fix several issues in the chunk-aligning feature.</li>
    <li><a href="https://codemirror.net/5/mode/verilog">verilog mode</a>: Rewritten to address various issues.</li>
    <li><a href="https://codemirror.net/5/mode/julia">julia mode</a>: Recognize Julia 0.5 syntax.</li>
    <li><a href="https://codemirror.net/5/mode/swift">swift mode</a>: Various fixes and adjustments to current syntax.</li>
    <li><a href="https://codemirror.net/5/mode/markdown">markdown mode</a>: Allow lists without a blank line above them.</li>
    <li>The <a href="https://codemirror.net/5/doc/manual.html#setGutterMarker"><code>setGutterMarker</code></a>, <a href="https://codemirror.net/doc/manual.html#clearGutter"><code>clearGutter</code></a>, and <a href="https://codemirror.net/doc/manual.html#lineInfo"><code>lineInfo</code></a> methods are now available on <code>Doc</code> objects.</li>
    <li>The <a href="https://codemirror.net/5/doc/manual.html#heightAtLine"><code>heightAtLine</code></a> method now takes an extra argument to allow finding the height at the top of the line's line widgets.</li>
    <li><a href="https://codemirror.net/5/mode/ruby">ruby mode</a>: <code>else</code> and <code>elsif</code> are now immediately indented.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Bind Ctrl-T and Ctrl-D to in- and dedent in insert mode.</li>
  </ul>

  <p class="rel">20-10-2016: <a href="https://codemirror.net/5/codemirror-5.20.0.zip">Version 5.20.0</a>:</p>

  <ul class=rel-note>
    <li>Make <code>newlineAndIndent</code> command work with multiple cursors on the same line.</li>
    <li>Make sure keypress events for backspace are ignored.</li>
    <li>Tokens styled with overlays no longer get a nonsense <code>cm-cm-overlay</code> class.</li>
    <li>Line endings for pasted content are now normalized to the editor's <a href="https://codemirror.net/5/doc/manual.html#option_lineSeparator">preferred ending</a>.</li>
    <li><a href="https://codemirror.net/5/mode/javascript">javascript mode</a>: Improve support for class expressions. Support TypeScript optional class properties, the <code>abstract</code> keyword, and return type declarations for arrow functions.</li>
    <li><a href="https://codemirror.net/5/mode/css">css mode</a>: Fix highlighting of mixed-case keywords.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_closebrackets">closebrackets addon</a>: Improve behavior when typing a quote before a string.</li>
    <li>The core is now maintained as a number of small files, using ES6 syntax and modules, under the <code>src/</code> directory. A git checkout no longer contains a working <code>codemirror.js</code> until you <code>npm run build</code> (but when installing from NPM, it is included).</li>
    <li>The <a href="https://codemirror.net/5/doc/manual.html#event_refresh"><code>refresh</code></a> event is now documented and stable.</li>
  </ul>

  <p class="rel">20-09-2016: <a href="https://codemirror.net/5/codemirror-5.19.0.zip">Version 5.19.0</a>:</p>

  <ul class=rel-note>
    <li><a href="https://codemirror.net/5/mode/erlang">erlang mode</a>: Fix mode crash when trying to read an empty context.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_comment">comment addon</a>: Fix broken behavior when toggling comments inside a comment.</li>
    <li>xml-fold addon: Fix a null-dereference bug.</li>
    <li>Page up and page down now do something even in single-line documents.</li>
    <li>Fix an issue where the cursor position could be off in really long (~8000 character) tokens.</li>
    <li><a href="https://codemirror.net/5/mode/javascript">javascript mode</a>: Better indentation when semicolons are missing. Better support for TypeScript classes, optional parameters, and the <code>type</code> keyword.</li>
    <li>The <a href="https://codemirror.net/5/doc/manual.html#event_blur"><code>blur</code></a> and <a href="https://codemirror.net/doc/manual.html#event_focus"><code>focus</code></a> events now pass the DOM event to their handlers.</li>
  </ul>

  <p class="rel">23-08-2016: <a href="https://codemirror.net/5/codemirror-5.18.2.zip">Version 5.18.2</a>:</p>

  <ul class=rel-note>
    <li><a href="https://codemirror.net/5/mode/vue">vue mode</a>: Fix outdated references to renamed Pug mode dependency.</li>
  </ul>

  <p class="rel">22-08-2016: <a href="https://codemirror.net/5/codemirror-5.18.0.zip">Version 5.18.0</a>:</p>

  <ul class=rel-note>
    <li>Make sure <a href="https://codemirror.net/5/doc/manual.html#addLineClass">gutter backgrounds</a> stick to the rest of the gutter during horizontal scrolling.</li>
    <li>The contenteditable <a href="https://codemirror.net/5/doc/manual.html#option_inputStyle"><code>inputStyle</code></a> now properly supports pasting on pre-Edge IE versions.</li>
    <li><a href="https://codemirror.net/5/mode/javascript">javascript mode</a>: Fix some small parsing bugs and improve TypeScript support.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_matchbrackets">matchbrackets addon</a>: Fix bug where active highlighting was left in editor when the addon was disabled.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_match-highlighter">match-highlighter addon</a>: Only start highlighting things when the editor gains focus.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_javascript-hint">javascript-hint addon</a>: Also complete non-enumerable properties.</li>
    <li>The <a href="https://codemirror.net/5/doc/manual.html#addOverlay"><code>addOverlay</code></a> method now supports a <code>priority</code> option to control the order in which overlays are applied.</li>
    <li>MIME types that end in <code>+json</code> now default to the JSON mode when the MIME itself is not defined.</li>
    <li>The mode formerly known as Jade was renamed to <a href="https://codemirror.net/5/mode/pug">Pug</a>.</li>
    <li>The <a href="https://codemirror.net/5/mode/python">Python mode</a> now defaults to Python 3 (rather than 2) syntax.</li>
  </ul>

  <p class="rel">19-07-2016: <a href="https://codemirror.net/5/codemirror-5.17.0.zip">Version 5.17.0</a>:</p>

  <ul class="rel-note">
    <li>Fix problem with wrapped trailing whitespace displaying incorrectly.</li>
    <li>Prevent IME dialog from overlapping typed content in Chrome.</li>
    <li>Improve measuring of characters near a line wrap.</li>
    <li><a href="https://codemirror.net/5/mode/javascript">javascript mode</a>: Improve support for <code>async</code>, allow trailing commas in <code>import</code> lists.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Fix backspace in replace mode.</li>
    <li><a href="https://codemirror.net/5/demo/sublime.html">sublime bindings</a>: Fix some key bindings on OS X to match Sublime Text.</li>
    <li><a href="https://codemirror.net/5/mode/markdown">markdown mode</a>: Add more classes to image links in highlight-formatting mode.</li>
  </ul>

  <p class="rel">20-06-2016: <a href="https://codemirror.net/5/codemirror-5.16.0.zip">Version 5.16.0</a>:</p>

  <ul class="rel-note">
    <li>Fix glitches when dragging content caused by the drop indicator receiving mouse events.</li>
    <li>Make Control-drag work on Firefox.</li>
    <li>Make clicking or selection-dragging at the end of a wrapped line select the right position.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_show-hint">show-hint addon</a>: Prevent widget scrollbar from hiding part of the hint text.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_rulers">rulers addon</a>: Prevent rulers from forcing a horizontal editor scrollbar.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_search">search addon</a>: Automatically bind search-related keys in persistent dialog.</li>
    <li><a href="https://codemirror.net/5/demo/sublime.html">sublime keymap</a>: Add a multi-cursor aware smart backspace binding.</li>
  </ul>

  <p class="rel">20-05-2016: <a href="https://codemirror.net/5/codemirror-5.15.2.zip">Version 5.15.2</a>:</p>

  <ul class="rel-note">
    <li>Fix a critical document corruption bug that occurs when a document is gradually grown.</li>
  </ul>

  <p class="rel">20-05-2016: <a href="https://codemirror.net/5/codemirror-5.15.0.zip">Version 5.15.0</a>:</p>

  <ul class="rel-note">
    <li>Fix bug that caused the selection to reset when focusing the editor in contentEditable input mode.</li>
    <li>Fix issue where not all ASCII control characters were being replaced by placeholders.</li>
    <li>Remove the assumption that all modes have a <code>startState</code> method from several wrapping modes.</li>
    <li>Fix issue where the editor would complain about overlapping collapsed ranges when there weren't any.</li>
    <li>Optimize document tree building when loading or pasting huge chunks of content.</li>
    <li>Explicitly bind Ctrl-O on OS X to make that binding (“open line”) act as expected.</li>
    <li>Pasting <a href="https://codemirror.net/5/doc/manual.html#option_lineWiseCopyCut">linewise-copied</a> content when there is no selection now inserts the lines above the current line.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">markdown mode</a>: Fix several issues in matching link targets.</li>
    <li><a href="https://codemirror.net/5/mode/clike/">clike mode</a>: Improve indentation of C++ template declarations.</li>
    <li><a href="https://codemirror.net/5/mode/javascript/">javascript mode</a>: Support <code>async</code>/<code>await</code> and improve support for TypeScript type syntax.</li>
  </ul>

  <p class="rel">20-04-2016: <a href="https://codemirror.net/5/codemirror-5.14.0.zip">Version 5.14.0</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/doc/manual.html#posFromIndex"><code>posFromIndex</code></a> and <a href="https://codemirror.net/doc/manual.html#indexFromPos"><code>indexFromPos</code></a> now take <a href="https://codemirror.net/doc/manual.html#option_lineSeparator"><code>lineSeparator</code></a> into account</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Only call <code>.save()</code> when it is actually available</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_comment">comment addon</a>: Be careful not to mangle multi-line strings</li>
    <li><a href="https://codemirror.net/5/mode/python/index.html">Python mode</a>: Improve distinguishing of decorators from <code>@</code> operators</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#findMarks"><code>findMarks</code></a>: No longer return marks that touch but don't overlap given range</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">vim bindings</a>: Add yank command</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_match-highlighter">match-highlighter addon</a>: Add <code>trim</code> option to disable ignoring of whitespace</li>
    <li><a href="https://codemirror.net/5/mode/powershell/index.html">PowerShell mode</a>: Added</li>
    <li><a href="https://codemirror.net/5/mode/yacas/index.html">Yacas mode</a>: Added</li>
    <li><a href="https://codemirror.net/5/mode/webidl/index.html">Web IDL mode</a>: Added</li>
    <li><a href="https://codemirror.net/5/mode/sas/index.html">SAS mode</a>: Added</li>
    <li><a href="https://codemirror.net/5/mode/mbox/index.html">mbox mode</a>: Added</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.13.4...5.14.0">list of patches</a></li>
  </ul>

  <p class="rel">21-03-2016: <a href="https://codemirror.net/5/codemirror-5.13.2.zip">Version 5.13.2</a>:</p>

  <ul class="rel-note">
    <li>Solves a problem where the gutter would sometimes not extend all the way to the end of the document.</li>
  </ul>

  <p class="rel">21-03-2016: <a href="https://codemirror.net/5/codemirror-5.13.zip">Version 5.13</a>:</p>

  <ul class="rel-note">
    <li>New DOM event forwarded: <a href="https://codemirror.net/5/doc/manual.html#event_dom"><code>&quot;dragleave&quot;</code></a>.</li>
    <li><a href="https://codemirror.net/5/mode/protobuf/index.html">protobuf mode</a>: Newly added.</li>
    <li>Fix problem where <a href="https://codemirror.net/5/doc/manual.html#findMarks"><code>findMarks</code></a> sometimes failed to find multi-line marks.</li>
    <li>Fix crash that showed up when atomic ranges and bidi text were combined.</li>
    <li><a href="https://codemirror.net/5/demo/complete.html">show-hint addon</a>: Completion widgets no longer close when the line indented or dedented.</li>
    <li><a href="https://codemirror.net/5/demo/merge.html">merge addon</a>: Fix bug when merging chunks at the end of the file.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_placeholder">placeholder addon</a>: No longer gets confused by <a href="https://codemirror.net/doc/manual.html#swapDoc"><code>swapDoc</code></a>.</li>
    <li><a href="https://codemirror.net/5/doc/manual.html#addon_simplescrollbars">simplescrollbars addon</a>: Fix invalid state when deleting at end of document.</li>
    <li><a href="https://codemirror.net/5/mode/clike/index.html">clike mode</a>: No longer gets confused when a comment starts after an operator.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/index.html">markdown mode</a>: Now supports CommonMark-style flexible list indentation.</li>
    <li><a href="https://codemirror.net/5/mode/dylan/index.html">dylan mode</a>: Several improvements and fixes.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.12.0...5.13.0">list of patches</a></li>
  </ul>

  <p class="rel">19-02-2016: <a href="https://codemirror.net/5/codemirror-5.12.zip">Version 5.12</a>:</p>

  <ul class="rel-note">
    <li><a href="https://codemirror.net/5/demo/vim.html">Vim bindings</a>: Ctrl-Q is now an alias for Ctrl-V.</li>
    <li><a href="https://codemirror.net/5/demo/vim.html">Vim bindings</a>: The Vim API now exposes an <code>unmap</code> method to unmap bindings.</li>
    <li><a href="https://codemirror.net/5/demo/activeline.html">active-line addon</a>: This addon can now style the active line's gutter.</li>
    <li><a href="https://codemirror.net/5/mode/fcl/">FCL mode</a>: Newly added.</li>
    <li><a href="https://codemirror.net/5/mode/sql/">SQL mode</a>: Now has a Postgresql dialect.</li>
    <li>Fix <a href="https://github.com/codemirror/CodeMirror/issues/3781">issue</a> where trying to scroll to a horizontal position outside of the document's width could cause the gutter to be positioned incorrectly.</li>
    <li>Use absolute, rather than fixed positioning in the context-menu intercept hack, to work around a <a href="https://github.com/codemirror/CodeMirror/issues/3238">problem</a> when the editor is inside a transformed parent container.</li>
    <li>Solve a <a href="https://github.com/codemirror/CodeMirror/issues/3821">problem</a> where the horizontal scrollbar could hide text in Firefox.</li>
    <li>Fix a <a href="https://github.com/codemirror/CodeMirror/issues/3834">bug</a> that caused phantom scroll space under the text in some situations.</li>
    <li><a href="https://codemirror.net/5/demo/sublime.html">Sublime Text bindings</a>: Bind delete-line to Shift-Ctrl-K on OS X.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">Markdown mode</a>: Fix <a href="https://github.com/codemirror/CodeMirror/issues/3787">issue</a> where the mode would keep state related to fenced code blocks in an unsafe way, leading to occasional corrupted parses.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">Markdown mode</a>: Ignore backslashes in code fragments.</li>
    <li><a href="https://codemirror.net/5/mode/markdown/">Markdown mode</a>: Use whichever mode is registered as <code>text/html</code> to parse HTML.</li>
    <li><a href="https://codemirror.net/5/mode/clike/">Clike mode</a>: Improve indentation of Scala <code>=&gt;</code> functions.</li>
    <li><a href="https://codemirror.net/5/mode/python/">Python mode</a>: Improve indentation of bracketed code.</li>
    <li><a href="https://codemirror.net/5/mode/htmlmixed/">HTMLMixed mode</a>: Support multi-line opening tags for sub-languages (<code>&lt;script&gt;</code>, <code>&lt;style&gt;</code>, etc).</li>
    <li><a href="https://codemirror.net/5/mode/spreadsheet/">Spreadsheet mode</a>: Fix bug where the mode did not advance the stream when finding a backslash.</li>
    <li><a href="https://codemirror.net/5/mode/xml/">XML mode</a>: The mode now takes a <code>matchClosing</code> option to configure whether mismatched closing tags should be highlighted as errors.</li>
  </ul>

  <p class="rel">20-01-2016: <a href="https://codemirror.net/5/codemirror-5.11.zip">Version 5.11</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/jsx/index.html">JSX</a>, <a href="../mode/haskell-literate/index.html">literate Haskell</a></li>
    <li>The editor now forwards more <a href="manual.html#event_dom">DOM events</a>: <code>cut</code>, <code>copy</code>, <code>paste</code>, and <code>touchstart</code>. It will also forward <code>mousedown</code> for drag events</li>
    <li>Fixes a bug where bookmarks next to collapsed spans were not rendered</li>
    <li>The <a href="../mode/swift/index.html">Swift</a> mode now supports auto-indentation</li>
    <li>Frontmatters in the <a href="../mode/yaml-frontmatter/index.html">YAML frontmatter</a> mode are now optional as intended</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.10.0...5.11.0">list of patches</a></li>
  </ul>

  <p class="rel">21-12-2015: <a href="https://codemirror.net/5/codemirror-5.10.zip">Version 5.10</a>:</p>

  <ul class="rel-note">
    <li>Modify the way <a href="manual.html#mark_atomic">atomic ranges</a> are skipped by selection to try and make it less surprising.</li>
    <li>The <a href="../mode/swift/index.html">Swift mode</a> was rewritten.</li>
    <li>New addon: <a href="manual.html#addon_jump-to-line">jump-to-line</a>.</li>
    <li>New method: <a href="manual.html#isReadOnly"><code>isReadOnly</code></a>.</li>
    <li>The <a href="manual.html#addon_show-hint">show-hint addon</a> now defaults to picking completions on single click.</li>
    <li>The object passed to <a href="manual.html#event_beforeSelectionChange"><code>&quot;beforeSelectionChange&quot;</code></a> events now has an <code>origin</code> property.</li>
    <li>New mode: <a href="../mode/crystal/index.html">Crystal</a>.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.9.0...5.10.0">list of patches</a></li>
  </ul>

  <p class="rel">23-11-2015: <a href="https://codemirror.net/5/codemirror-5.9.zip">Version 5.9</a>:</p>

  <ul class="rel-note">
    <li>Improve the way overlay (OS X-style) scrollbars are handled</li>
    <li>Make <a href="manual.html#addon_annotatescrollbar">annotatescrollbar</a> and scrollpastend addons work properly together</li>
    <li>Make <a href="manual.html#addon_show-hint">show-hint</a> addon select options on single click by default, move selection to hovered item</li>
    <li>Properly fold comments that include block-comment-start markers</li>
    <li>Many small language mode fixes</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.8.0...5.9.0">list of patches</a></li>
  </ul>

  <p class="rel">20-10-2015: <a href="https://codemirror.net/5/codemirror-5.8.zip">Version 5.8</a>:</p>

  <ul class="rel-note">
    <li>Fixes an infinite loop in
    the <a href="manual.html#addon_hardwrap">hardwrap
    addon</a></li>
    <li>New modes: <a href="../mode/nsis/index.html">NSIS</a>, <a href="../mode/clike/index.html">Ceylon</a></li>
    <li>The Kotlin mode is now a <a href="../mode/clike/index.html">clike</a> dialect, rather than a stand-alone mode</li>
    <li>New option: <a href="manual.html#option_allowDropFileTypes"><code>allowDropFileTypes</code></a>. Binary files can no longer be dropped into CodeMirror</li>
    <li>New themes: <a href="../demo/theme.html#bespin">bespin</a>, <a href="../demo/theme.html#hopscotch">hopscotch</a>, <a href="../demo/theme.html#isotope">isotope</a>, <a href="../demo/theme.html#railscasts">railscasts</a></li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.7.0...5.8.0">list of patches</a></li>
  </ul>

  <p class="rel">20-09-2015: <a href="https://codemirror.net/5/codemirror-5.7.zip">Version 5.7</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/vue/index.html">Vue</a>, <a href="../mode/oz/index.html">Oz</a>, <a href="../mode/mscgen/index.html">MscGen</a> (and dialects), <a href="../mode/css/gss.html">Closure Stylesheets</a></li>
    <li>Implement <a href="http://commonmark.org">CommonMark</a>-style flexible list indent and cross-line code spans in <a href="../mode/markdown/index.html">Markdown</a> mode</li>
    <li>Add a replace-all button to the <a href="manual.html#addon_search">search addon</a>, and make the persistent search dialog transparent when it obscures the match</li>
    <li>Handle <code>async</code>/<code>await</code> and ocal and binary numbers in <a href="../mode/javascript/index.html">JavaScript mode</a></li>
    <li>Fix various issues with the <a href="../mode/haxe/index.html">Haxe mode</a></li>
    <li>Make the <a href="manual.html#addon_closebrackets">closebrackets addon</a> select only the wrapped text when wrapping selection in brackets</li>
    <li>Tokenize properties as properties in the <a href="../mode/coffeescript/index.html">CoffeeScript mode</a></li>
    <li>The <a href="manual.html#addon_placeholder">placeholder addon</a> now accepts a DOM node as well as a string placeholder</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.6.0...5.7.0">list of patches</a></li>
  </ul>

  <p class="rel">20-08-2015: <a href="https://codemirror.net/5/codemirror-5.6.zip">Version 5.6</a>:</p>

  <ul class="rel-note">
    <li>Fix bug where you could paste into a <code>readOnly</code> editor</li>
    <li>Show a cursor at the drop location when dragging over the editor</li>
    <li>The <a href="../mode/rust/index.html">Rust mode</a> was rewritten to handle modern Rust</li>
    <li>The editor and theme CSS was cleaned up. Some selectors are now less specific than before</li>
    <li>New theme: <a href="../demo/theme.html#abcdef">abcdef</a></li>
    <li>Lines longer than <a href="manual.html#option_maxHighlightLength"><code>maxHighlightLength</code></a> are now less likely to mess up indentation</li>
    <li>New addons: <a href="manual.html#addon_autorefresh"><code>autorefresh</code></a> for refreshing an editor the first time it becomes visible, and <code>html-lint</code> for using <a href="http://htmlhint.com/">HTMLHint</a></li>
    <li>The <a href="manual.html#addon_search"><code>search</code></a> addon now recognizes <code>\r</code> and <code>\n</code> in pattern and replacement input</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.5.0...5.6.0">list of patches</a></li>
  </ul>

  <p class="rel">20-07-2015: <a href="https://codemirror.net/5/codemirror-5.5.zip">Version 5.5</a>:</p>

  <ul class="rel-note">
    <li>New option: <a href="manual.html#option_lineSeparator"><code>lineSeparator</code></a> (with corresponding <a href="manual.html#lineSeparator">method</a>)
    <li>New themes: <a href="../demo/theme.html#dracula">dracula</a>, <a href="../demo/theme.html#seti">seti</a>, <a href="../demo/theme.html#yeti">yeti</a>, <a href="../demo/theme.html#material">material</a>, and <a href="../demo/theme.html#icecoder">icecoder</a></li>
    <li>New modes: <a href="../mode/brainfuck/index.html">Brainfuck</a>, <a href="../mode/vhdl/index.html">VHDL</a>, Squirrel (<a href="../mode/clike/index.html">clike</a> dialect)</li>
    <li>Define a <code>findPersistent</code> command in
    the <a href="../demo/search.html">search</a> addon, for a dialog
    that stays open as you cycle through matches</li>
    <li>From this release on, the NPM module doesn't include documentation and demos</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.4.0...5.5.0">list of patches</a></li>
  </ul>

  <p class="rel">25-06-2015: <a href="https://codemirror.net/5/codemirror-5.4.zip">Version 5.4</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/twig/index.html">Twig</a>, <a href="../mode/elm/index.html">Elm</a>, <a href="../mode/factor/index.html">Factor</a>, <a href="../mode/swift/index.html">Swift</a></li>
    <li>Prefer clipboard API (if available) when pasting</li>
    <li>Refined definition highlighting in <a href="../mode/clike/index.html">clike</a> mode</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.3.0...5.4.0">list of patches</a></li>
  </ul>

  <p class="rel">20-05-2015: <a href="https://codemirror.net/5/codemirror-5.3.zip">Version 5.3</a>:</p>

  <ul class="rel-note">
    <li>Fix several regressions in the <a href="manual.html#addon_show-hint"><code>show-hint</code></a> addon (<code>completeSingle</code> option, <code>"shown"</code> and <code>"close"</code> events)</li>
    <li>The <a href="../demo/vim.html">vim mode</a> API was <a href="manual.html#vimapi">documented</a></li>
    <li>New modes: <a href="../mode/asn.1/index.html">ASN.1</a>, <a href="../mode/ttcn/index.html">TTCN</a>, and <a href="../mode/ttcn-cfg/index.html">TTCN-CFG</a></li>
    <li>The <a href="../mode/clike/index.html">clike</a> mode can now deep-indent <code>switch</code> statements, and roughly recognizes types and defined identifiers</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.2.0...5.3.0">list of patches</a></li>
  </ul>

  <p class="rel">20-04-2015: <a href="https://codemirror.net/5/codemirror-5.2.zip">Version 5.2</a>:</p>

  <ul class="rel-note">
    <li>Fix several race conditions
    in <a href="manual.html#addon_show-hint"><code>show-hint</code></a>'s
    asynchronous mode</li>
    <li>Fix backspace binding in <a href="../demo/sublime.html">Sublime bindings</a></li>
    <li>Change the way IME is handled in the <code>"textarea"</code> <a href="manual.html#option_inputStyle">input style</a></li>

    <li>New modes: <a href="../mode/mumps/index.html">MUMPS</a>, <a href="../mode/handlebars/index.html">Handlebars</a></li>
    <li>Rewritten modes: <a href="../mode/django/index.html">Django</a>, <a href="../mode/z80/index.html">Z80</a></li>
    <li>New theme: <a href="../demo/theme.html#liquibyte">Liquibyte</a></li>
    <li>New option: <a href="manual.html#option_lineWiseCopyCut"><code>lineWiseCopyCut</code></a></li>
    <li>The <a href="../demo/vim.html">Vim mode</a> now supports buffer-local options and the <code>filetype</code> setting</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.1.0...5.2.0">list of patches</a></li>
  </ul>

  <p class="rel">23-03-2015: <a href="https://codemirror.net/5/codemirror-5.1.zip">Version 5.1</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/asciiarmor/index.html">ASCII armor</a> (PGP data), <a href="../mode/troff/index.html">Troff</a>, and <a href="../mode/cmake/index.html">CMake</a>.</li>
    <li>Remove SmartyMixed mode, rewrite <a href="../mode/smarty/index.html">Smarty</a> mode to supersede it.</li>
    <li>New commands in the <a href="manual.html#addon_merge">merge
    addon</a>: <code>goNextDiff</code> and <code>goPrevDiff</code>.</li>
    <li>The <a href="manual.html#addon_closebrackets">closebrackets addon</a> can now be configured per mode.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/5.0.0...5.1.0">list of patches</a>.</li>
  </ul>

  <p class="rel">20-02-2015: <a href="https://codemirror.net/5/codemirror-5.0.zip">Version 5.0</a>:</p>

  <ul class="rel-note">
    <li>Experimental mobile support (tested on iOS, Android Chrome, stock Android browser)</li>
    <li>New option <a href="manual.html#option_inputStyle"><code>inputStyle</code></a> to switch between hidden textarea and contenteditable input.</li>
    <li>The <a href="manual.html#getInputField"><code>getInputField</code></a>
    method is no longer guaranteed to return a textarea.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.13.0...5.0.0">list of patches</a>.</li>
  </ul>

</section>

<section id=v4 class=first>

  <h2>Version 4.x</h2>

  <p class="rel">20-02-2015: <a href="https://codemirror.net/5/codemirror-4.13.zip">Version 4.13</a>:</p>

  <ul class="rel-note">
    <li>Fix the way the <a href="../demo/closetag.html"><code>closetag</code></a> demo handles the slash character.</li>
    <li>New modes: <a href="../mode/forth/index.html">Forth</a>, <a href="../mode/stylus/index.html">Stylus</a>.</li>
    <li>Make the <a href="../mode/css/index.html">CSS mode</a> understand some modern CSS extensions.</li>
    <li>Have the <a href="../mode/clike/index.html">Scala mode</a> handle symbols and triple-quoted strings.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.12.0...4.13.0">list of patches</a>.</li>
  </ul>

  <p class="rel">22-01-2015: <a href="https://codemirror.net/5/codemirror-4.12.zip">Version 4.12</a>:</p>

  <ul class="rel-note">
    <li>The <a href="manual.html#addon_closetag"><code>closetag</code></a>
    addon now defines a <code>"closeTag"</code> command.</li>
    <li>Adds a <code>findModeByFileName</code> to the <a href="manual.html#addon_meta">mode metadata</a>
    addon.</li>
    <li><a href="../demo/simplemode.html">Simple mode</a> rules can
    now contain a <code>sol</code> property to only match at the start
    of a line.</li>
    <li>New
    addon: <a href="manual.html#addon_selection-pointer"><code>selection-pointer</code></a>
    to style the mouse cursor over the selection.</li>
    <li>Improvements to the <a href="../mode/sass/index.html">Sass mode</a>'s indentation.</li>
    <li>The <a href="../demo/vim.html">Vim keymap</a>'s search functionality now
    supports <a href="manual.html#addon_matchesonscrollbar">scrollbar
    annotation</a>.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.11.0...4.12.0">list of patches</a>.</li>
  </ul>

  <p class="rel">9-01-2015: <a href="https://codemirror.net/5/codemirror-4.11.zip">Version 4.11</a>:</p>

  <p class="rel-note">Unfortunately, 4.10 did not take care of the
  Firefox scrolling issue entirely. This release adds two more patches
  to address that.</p>

  <p class="rel">29-12-2014: <a href="https://codemirror.net/5/codemirror-4.10.zip">Version 4.10</a>:</p>

  <p class="rel-note">Emergency single-patch update to 4.9. Fixes
  Firefox-specific problem where the cursor could end up behind the
  horizontal scrollbar.</p>

  <p class="rel">23-12-2014: <a href="https://codemirror.net/5/codemirror-4.9.zip">Version 4.9</a>:</p>

  <ul class="rel-note">
    <li>Overhauled scroll bar handling.
    Add pluggable <a href="../demo/simplescrollbars.html">scrollbar
    implementations</a>.</li>
    <li>Tweaked behavior for
    the <a href="manual.html#addon_show-hint">completion addons</a> to
    not take text after cursor into account.</li>
    <li>Two new optional features in
    the <a href="manual.html#addon_merge">merge addon</a>: aligning
    editors, and folding unchanged text.</li>
    <li>New
    modes: <a href="../mode/dart/index.html">Dart</a>, <a href="../mode/ebnf/index.html">EBNF</a>, <a href="../mode/spreadsheet/index.html">spreadsheet</a>,
    and <a href="../mode/soy/index.html">Soy</a>.</li>
    <li>New <a href="../demo/panel.html">addon</a> to show persistent panels below/above an editor.</li>
    <li>New themes: <a href="../demo/theme.html#zenburn">zenburn</a>
    and <a href="../demo/theme.html#tomorrow-night-bright">tomorrow night
    bright</a>.</li>
    <li>Allow ctrl-click to clear existing cursors.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.8.0...4.9.0">list of patches</a>.</li>
  </ul>

  <p class="rel">22-11-2014: <a href="https://codemirror.net/5/codemirror-4.8.zip">Version 4.8</a>:</p>

  <ul class="rel-note">
    <li>Built-in support for <a href="manual.html#normalizeKeyMap">multi-stroke key bindings</a>.</li>
    <li>New method: <a href="manual.html#getLineTokens"><code>getLineTokens</code></a>.</li>
    <li>New modes: <a href="../mode/dockerfile/index.html">dockerfile</a>, <a href="../mode/idl/index.html">IDL</a>, <a href="../mode/clike/index.html">Objective C</a> (crude).</li>
    <li>Support styling of gutter backgrounds, allow <code>"gutter"</code> styles in <a href="manual.html#addLineClass"><code>addLineClass</code></a>.</li>
    <li>Many improvements to the <a href="../demo/vim.html">Vim mode</a>, rewritten visual mode.</li>
    <li>Improvements to modes: <a href="../mode/gfm/index.html">gfm</a> (strikethrough), <a href="../mode/sparql/index.html">SPARQL</a> (version 1.1 support), and <a href="../mode/stex/index.html">sTeX</a> (no more runaway math mode).
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.7.0...4.8.0">list of patches</a>.</li>
  </ul>

  <p class="rel">20-10-2014: <a href="https://codemirror.net/5/codemirror-4.7.zip">Version 4.7</a>:</p>

  <ul class="rel-note">
    <li><strong>Incompatible</strong>:
    The <a href="../demo/lint.html">lint addon</a> now passes the
    editor's value as first argument to asynchronous lint functions,
    for consistency. The editor is still passed, as fourth
    argument.</li>
    <li>Improved handling of unicode identifiers in modes for
    languages that support them.</li>
    <li>More mode
    improvements: <a href="../mode/coffeescript/index.html">CoffeeScript</a>
    (indentation), <a href="../mode/verilog/index.html">Verilog</a>
    (indentation), <a href="../mode/clike/index.html">Scala</a>
    (indentation, triple-quoted strings),
    and <a href="../mode/php/index.html">PHP</a> (interpolated
    variables in heredoc strings).</li>
    <li>New modes: <a href="../mode/textile/index.html">Textile</a> and <a href="../mode/tornado/index.html">Tornado templates</a>.</li>
    <li>Experimental new <a href="../demo/simplemode.html">way to define modes</a>.</li>
    <li>Improvements to the <a href="../demo/vim.html">Vim
    bindings</a>: Arbitrary insert mode key mappings are now possible,
    and text objects are supported in visual mode.</li>
    <li>The mode <a href="../mode/meta.js">meta-information file</a>
    now includes information about file extensions,
    and <a href="manual.html#addon_meta">helper
    functions</a> <code>findModeByMIME</code>
    and <code>findModeByExtension</code>.</li>
    <li>New logo!</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.6.0...4.7.0">list of patches</a>.</li>
  </ul>

  <p class="rel">19-09-2014: <a href="https://codemirror.net/5/codemirror-4.6.zip">Version 4.6</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/modelica/index.html">Modelica</a></li>
    <li>New method: <a href="manual.html#findWordAt"><code>findWordAt</code></a></li>
    <li>Make it easier to <a href="../demo/markselection.html">use text background styling</a></li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.5.0...4.6.0">list of patches</a>.</li>
  </ul>

  <p class="rel">21-08-2014: <a href="https://codemirror.net/5/codemirror-4.5.zip">Version 4.5</a>:</p>

  <ul class="rel-note">
    <li>Fix several serious bugs with horizontal scrolling</li>
    <li>New mode: <a href="../mode/slim/index.html">Slim</a></li>
    <li>New command: <a href="manual.html#command_goLineLeftSmart"><code>goLineLeftSmart</code></a></li>
    <li>More fixes and extensions for the <a href="../demo/vim.html">Vim</a> visual block mode</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.4.0...4.5.0">list of patches</a>.</li>
  </ul>

  <p class="rel">21-07-2014: <a href="https://codemirror.net/5/codemirror-4.4.zip">Version 4.4</a>:</p>

  <ul class="rel-note">
    <li><strong>Note:</strong> Some events might now fire in slightly
    different order (<code>"change"</code> is still guaranteed to fire
    before <code>"cursorActivity"</code>)</li>
    <li>Nested operations in multiple editors are now synced (complete
    at same time, reducing DOM reflows)</li>
    <li>Visual block mode for <a href="../demo/vim.html">vim</a> (&lt;C-v>) is nearly complete</li>
    <li>New mode: <a href="../mode/kotlin/index.html">Kotlin</a></li>
    <li>Better multi-selection paste for text copied from multiple CodeMirror selections</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.3.0...4.4.0">list of patches</a>.</li>
  </ul>

  <p class="rel">23-06-2014: <a href="https://codemirror.net/5/codemirror-4.3.zip">Version 4.3</a>:</p>

  <ul class="rel-note">
    <li>Several <a href="../demo/vim.html">vim bindings</a>
    improvements: search and exCommand history, global flag
    for <code>:substitute</code>, <code>:global</code> command.
    <li>Allow hiding the cursor by
    setting <a href="manual.html#option_cursorBlinkRate"><code>cursorBlinkRate</code></a>
    to a negative value.</li>
    <li>Make gutter markers themeable, use this in foldgutter.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.2.0...4.3.0">list of patches</a>.</li>
  </ul>

  <p class="rel">19-05-2014: <a href="https://codemirror.net/5/codemirror-4.2.zip">Version 4.2</a>:</p>

  <ul class="rel-note">
    <li>Fix problem where some modes were broken by the fact that empty tokens were forbidden.</li>
    <li>Several fixes to context menu handling.</li>
    <li>On undo, scroll <em>change</em>, not cursor, into view.</li>
    <li>Rewritten <a href="../mode/jade/index.html">Jade</a> mode.</li>
    <li>Various improvements to <a href="../mode/shell/index.html">Shell</a> (support for more syntax) and <a href="../mode/python/index.html">Python</a> (better indentation) modes.</li>
    <li>New mode: <a href="../mode/cypher/index.html">Cypher</a>.</li>
    <li>New theme: <a href="../demo/theme.html#neo">Neo</a>.</li>
    <li>Support direct styling options (color, line style, width) in the <a href="manual.html#addon_rulers">rulers</a> addon.</li>
    <li>Recognize per-editor configuration for the <a href="manual.html#addon_show-hint">show-hint</a> and <a href="manual.html#addon_foldcode">foldcode</a> addons.</li>
    <li>More intelligent scanning for existing close tags in <a href="manual.html#addon_closetag">closetag</a> addon.</li>
    <li>In the <a href="../demo/vim.html">Vim bindings</a>: Fix bracket matching, support case conversion in visual mode, visual paste, append action.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.1.0...4.2.0">list of patches</a>.</li>
  </ul>

  <p class="rel">22-04-2014: <a href="https://codemirror.net/5/codemirror-4.1.zip">Version 4.1</a>:</p>

  <ul class="rel-note">
    <li><em>Slightly incompatible</em>:
    The <a href="manual.html#event_cursorActivity"><code>"cursorActivity"</code></a>
    event now fires after all other events for the operation (and only
    for handlers that were actually registered at the time the
    activity happened).</li>
    <li>New command: <a href="manual.html#command_insertSoftTab"><code>insertSoftTab</code></a>.</li>
    <li>New mode: <a href="../mode/django/index.html">Django</a>.</li>
    <li>Improved modes: <a href="../mode/verilog/index.html">Verilog</a> (rewritten), <a href="../mode/jinja2/index.html">Jinja2</a>, <a href="../mode/haxe/index.html">Haxe</a>, <a href="../mode/php/index.html">PHP</a> (string interpolation highlighted), <a href="../mode/javascript/index.html">JavaScript</a> (indentation of trailing else, template strings), <a href="../mode/livescript/index.html">LiveScript</a> (multi-line strings).</li>
    <li>Many small issues from the 3.x→4.x transition were found and fixed.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/4.0.3...4.1.0">list of patches</a>.</li>
  </ul>

  <p class="rel">20-03-2014: <a href="https://codemirror.net/5/codemirror-4.0.zip">Version 4.0</a>:</p>

  <p class="rel-note">This is a new major version of CodeMirror. There
  are a few <strong>incompatible</strong> changes in the API. Upgrade
  with care, and read the <a href="upgrade_v4.html">upgrading
  guide</a>.</p>

  <ul class="rel-note">
    <li>Multiple selections (ctrl-click, alt-drag, <a href="manual.html#setSelections">API</a>).</li>
    <li>Sublime Text <a href="../demo/sublime.html">bindings</a>.</li>
    <li><a href="manual.html#modloader">Module loader shims</a> wrapped around all modules.</li>
    <li>Selection <a href="manual.html#command_undoSelection">undo</a>/<a href="manual.html#command_redoSelection">redo</a>.</li>
    <li>Improved character measuring (faster, handles wrapped lines more robustly).</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/3.23.0...4.0.3">list of patches</a>.</li>
  </ul>

</section>

<section id=v3>

  <h2>Version 3.x</h2>

  <p class="rel">22-04-2014: <a href="https://codemirror.net/5/codemirror-3.24.zip">Version 3.24</a>:</p>

  <p class="rel-note">Merges the improvements from 4.1 that could
  easily be applied to the 3.x code. Also improves the way the editor
  size is updated when line widgets change.</p>

  <p class="rel">20-03-2014: <a href="https://codemirror.net/5/codemirror-3.23.zip">Version 3.23</a>:</p>

  <ul class="rel-note">
    <li>In the <a href="../mode/xml/index.html">XML mode</a>,
    add <code>brackets</code> style to angle brackets, fix
    case-sensitivity of tags for HTML.</li>
    <li>New mode: <a href="../mode/dylan/index.html">Dylan</a>.</li>
    <li>Many improvements to the <a href="../demo/vim.html">Vim bindings</a>.</li>
  </ul>

  <p class="rel">21-02-2014: <a href="https://codemirror.net/5/codemirror-3.22.zip">Version 3.22</a>:</p>

  <ul class="rel-note">
    <li>Adds the <a href="manual.html#findMarks"><code>findMarks</code></a> method.</li>
    <li>New addons: <a href="manual.html#addon_rulers">rulers</a>, markdown-fold, yaml-lint.</li>
    <li>New theme: <a href="../demo/theme.html#mdn-like">mdn-like</a>.</li>
    <li>New mode: <a href="../mode/solr/index.html">Solr</a>.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/3.21.0...3.22.0">list of patches</a>.</li>
  </ul>

  <p class="rel">16-01-2014: <a href="https://codemirror.net/5/codemirror-3.21.zip">Version 3.21</a>:</p>

  <ul class="rel-note">
    <li>Auto-indenting a block will no longer add trailing whitespace to blank lines.</li>
    <li>Marking text has a new option <a href="manual.html#markText"><code>clearWhenEmpty</code></a> to control auto-removal.</li>
    <li>Several bugfixes in the handling of bidirectional text.</li>
    <li>The <a href="../mode/xml/index.html">XML</a> and <a href="../mode/css/index.html">CSS</a> modes were largely rewritten. <a href="../mode/css/less.html">LESS</a> support was added to the CSS mode.</li>
    <li>The OCaml mode was moved to an <a href="../mode/mllike/index.html">mllike</a> mode, F# support added.</li>
    <li>Make it possible to fetch multiple applicable helper values with <a href="manual.html#getHelpers"><code>getHelpers</code></a>, and to register helpers matched on predicates with <a href="manual.html#registerGlobalHelper"><code>registerGlobalHelper</code></a>.</li>
    <li>New theme <a href="../demo/theme.html#pastel-on-dark">pastel-on-dark</a>.</li>
    <li>Better ECMAScript 6 support in <a href="../mode/javascript/index.html">JavaScript</a> mode.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/3.20.0...3.21.0">list of patches</a>.</li>
  </ul>

  <p class="rel">21-11-2013: <a href="https://codemirror.net/5/codemirror-3.20.zip">Version 3.20</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/julia/index.html">Julia</a> and <a href="../mode/pegjs/index.html">PEG.js</a>.</li>
    <li>Support ECMAScript 6 in the <a href="../mode/javascript/index.html">JavaScript mode</a>.</li>
    <li>Improved indentation for the <a href="../mode/coffeescript/index.html">CoffeeScript mode</a>.</li>
    <li>Make non-printable-character representation <a href="manual.html#option_specialChars">configurable</a>.</li>
    <li>Add ‘notification’ functionality to <a href="manual.html#addon_dialog">dialog</a> addon.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/3.19.0...3.20.0">list of patches</a>.</li>
  </ul>

  <p class="rel">21-10-2013: <a href="https://codemirror.net/5/codemirror-3.19.zip">Version 3.19</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/eiffel/index.html">Eiffel</a>, <a href="../mode/gherkin/index.html">Gherkin</a>, <a href="../mode/sql/?mime=text/x-mssql">MSSQL dialect</a>.</li>
    <li>New addons: <a href="manual.html#addon_hardwrap">hardwrap</a>, <a href="manual.html#addon_sql-hint">sql-hint</a>.</li>
    <li>New theme: <a href="../demo/theme.html#mbo">MBO</a>.</li>
    <li>Add <a href="manual.html#token_style_line">support</a> for line-level styling from mode tokenizers.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/3.18.0...3.19.0">list of patches</a>.</li>
  </ul>

  <p class="rel">23-09-2013: <a href="https://codemirror.net/5/codemirror-3.18.zip">Version 3.18</a>:</p>

  <p class="rel-note">Emergency release to fix a problem in 3.17
  where <code>.setOption("lineNumbers", false)</code> would raise an
  error.</p>

  <p class="rel">23-09-2013: <a href="https://codemirror.net/5/codemirror-3.17.zip">Version 3.17</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/fortran/index.html">Fortran</a>, <a href="../mode/octave/index.html">Octave</a> (Matlab), <a href="../mode/toml/index.html">TOML</a>, and <a href="../mode/dtd/index.html">DTD</a>.</li>
    <li>New addons: <a href="../addon/lint/css-lint.js"><code>css-lint</code></a>, <a href="manual.html#addon_css-hint"><code>css-hint</code></a>.</li>
    <li>Improve resilience to CSS 'frameworks' that globally mess up <code>box-sizing</code>.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/3.16.0...3.17.0">list of patches</a>.</li>
  </ul>

  <p class="rel">21-08-2013: <a href="https://codemirror.net/5/codemirror-3.16.zip">Version 3.16</a>:</p>

  <ul class="rel-note">
    <li>The whole codebase is now under a single <a href="../LICENSE">license</a> file.</li>
    <li>The project page was overhauled and redesigned.</li>
    <li>New themes: <a href="../demo/theme.html#paraiso-dark">Paraiso</a> (<a href="../demo/theme.html#paraiso-light">light</a>), <a href="../demo/theme.html#the-matrix">The Matrix</a>.</li>
    <li>Improved interaction between themes and <a href="manual.html#addon_active-line">active-line</a>/<a href="manual.html#addon_matchbrackets">matchbrackets</a> addons.</li>
    <li>New <a href="manual.html#addon_foldcode">folding</a> function <code>CodeMirror.fold.comment</code>.</li>
    <li>Added <a href="manual.html#addon_fullscreen">fullscreen</a> addon.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/3.15.0...3.16.0">list of patches</a>.</li>
  </ul>

  <p class="rel">29-07-2013: <a href="https://codemirror.net/5/codemirror-3.15.zip">Version 3.15</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/jade/index.html">Jade</a>, <a href="../mode/nginx/index.html">Nginx</a>.</li>
    <li>New addons: <a href="../demo/tern.html">Tern</a>, <a href="manual.html#addon_matchtags">matchtags</a>, and <a href="manual.html#addon_foldgutter">foldgutter</a>.</li>
    <li>Introduced <a href="manual.html#getHelper"><em>helper</em></a> concept (<a href="https://groups.google.com/forum/#!msg/codemirror/cOc0xvUUEUU/nLrX1-qnidgJ">context</a>).</li>
    <li>New method: <a href="manual.html#getModeAt"><code>getModeAt</code></a>.</li>
    <li>New themes: base16 <a href="../demo/theme.html#base16-dark">dark</a>/<a href="../demo/theme.html#base16-light">light</a>, 3024 <a href="../demo/theme.html#3024-night">dark</a>/<a href="../demo/theme.html#3024-day">light</a>, <a href="../demo/theme.html#tomorrow-night-eighties">tomorrow-night</a>.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/3.14.0...3.15.0">list of patches</a>.</li>
  </ul>

  <p class="rel">20-06-2013: <a href="https://codemirror.net/5/codemirror-3.14.zip">Version 3.14</a>:</p>

  <ul class="rel-note">
    <li>New
    addons: <a href="manual.html#addon_trailingspace">trailing
    space highlight</a>, <a href="manual.html#addon_xml-hint">XML
    completion</a> (rewritten),
    and <a href="manual.html#addon_merge">diff merging</a>.</li>
    <li><a href="manual.html#markText"><code>markText</code></a>
    and <a href="manual.html#addLineWidget"><code>addLineWidget</code></a>
    now take a <code>handleMouseEvents</code> option.</li>
    <li>New methods: <a href="manual.html#lineAtHeight"><code>lineAtHeight</code></a>,
    <a href="manual.html#getTokenTypeAt"><code>getTokenTypeAt</code></a>.</li>
    <li>More precise cleanness-tracking
    using <a href="manual.html#changeGeneration"><code>changeGeneration</code></a>
    and <a href="manual.html#isClean"><code>isClean</code></a>.</li>
    <li>Many extensions to <a href="../demo/emacs.html">Emacs</a> mode
    (prefixes, more navigation units, and more).</li>
    <li>New
    events <a href="manual.html#event_keyHandled"><code>"keyHandled"</code></a>
    and <a href="manual.html#event_inputRead"><code>"inputRead"</code></a>.</li>
    <li>Various improvements to <a href="../mode/ruby/index.html">Ruby</a>,
    <a href="../mode/smarty/index.html">Smarty</a>, <a href="../mode/sql/index.html">SQL</a>,
    and <a href="../demo/vim.html">Vim</a> modes.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/3.13.0...3.14.0">list of patches</a>.</li>
  </ul>

  <p class="rel">20-05-2013: <a href="https://codemirror.net/5/codemirror-3.13.zip">Version 3.13</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/cobol/index.html">COBOL</a> and <a href="../mode/haml/index.html">HAML</a>.</li>
    <li>New options: <a href="manual.html#option_cursorScrollMargin"><code>cursorScrollMargin</code></a> and <a href="manual.html#option_coverGutterNextToScrollbar"><code>coverGutterNextToScrollbar</code></a>.</li>
    <li>New addon: <a href="manual.html#addon_comment">commenting</a>.</li>
    <li>More features added to the <a href="../demo/vim.html">Vim keymap</a>.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v3.12...3.13.0">list of patches</a>.</li>
  </ul>

  <p class="rel">19-04-2013: <a href="https://codemirror.net/5/codemirror-3.12.zip">Version 3.12</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/gas/index.html">GNU assembler</a>.</li>
    <li>New
    options: <a href="manual.html#option_maxHighlightLength"><code>maxHighlightLength</code></a>
    and <a href="manual.html#option_historyEventDelay"><code>historyEventDelay</code></a>.</li>
    <li>Added <a href="manual.html#mark_addToHistory"><code>addToHistory</code></a>
    option for <code>markText</code>.</li>
    <li>Various fixes to JavaScript tokenization and indentation corner cases.</li>
    <li>Further improvements to the vim mode.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v3.11...v3.12">list of patches</a>.</li>
  </ul>

  <p class="rel">20-03-2013: <a href="https://codemirror.net/5/codemirror-3.11.zip">Version 3.11</a>:</p>

  <ul class="rel-note">
    <li><strong>Removed code:</strong> <code>collapserange</code>,
    <code>formatting</code>, and <code>simple-hint</code>
    addons. <code>plsql</code> and <code>mysql</code> modes
    (use <a href="../mode/sql/index.html"><code>sql</code></a> mode).</li>
    <li><strong>Moved code:</strong> the range-finding functions for folding now have <a href="../addon/fold/">their own files</a>.</li>
    <li><strong>Changed interface:</strong>
    the <a href="manual.html#addon_continuecomment"><code>continuecomment</code></a>
    addon now exposes an option, rather than a command.</li>
    <li>New
    modes: <a href="../mode/css/scss.html">SCSS</a>, <a href="../mode/tcl/index.html">Tcl</a>, <a href="../mode/livescript/index.html">LiveScript</a>,
    and <a href="../mode/mirc/index.html">mIRC</a>.</li>
    <li>New addons: <a href="../demo/placeholder.html"><code>placeholder</code></a>, <a href="../demo/html5complete.html">HTML completion</a>.</li>
    <li>New
    methods: <a href="manual.html#hasFocus"><code>hasFocus</code></a>, <a href="manual.html#defaultCharWidth"><code>defaultCharWidth</code></a>.</li>
    <li>New events: <a href="manual.html#event_beforeCursorEnter"><code>beforeCursorEnter</code></a>, <a href="manual.html#event_renderLine"><code>renderLine</code></a>.</li>
    <li>Many improvements to the <a href="manual.html#addon_show-hint"><code>show-hint</code></a> completion
    dialog addon.</li>
    <li>Tweak behavior of by-word cursor motion.</li>
    <li>Further improvements to the <a href="../demo/vim.html">vim mode</a>.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v3.1...v3.11">list of patches</a>.</li>
  </ul>

  <p class="rel">21-02-2013: <a href="https://codemirror.net/5/codemirror-3.1.zip">Version 3.1</a>:</p>

  <ul class="rel-note">
    <li><strong>Incompatible:</strong> key handlers may
    now <em>return</em>, rather
    than <em>throw</em> <code>CodeMirror.Pass</code> to signal they
    didn't handle the key.</li>
    <li>Make documents a <a href="manual.html#api_doc">first-class
    construct</a>, support split views and subviews.</li>
    <li>Add a <a href="manual.html#addon_show-hint">new module</a>
    for showing completion hints.
    Deprecate <code>simple-hint.js</code>.</li>
    <li>Extend <a href="../mode/htmlmixed/index.html">htmlmixed mode</a>
    to allow custom handling of script types.</li>
    <li>Support an <code>insertLeft</code> option
    to <a href="manual.html#setBookmark"><code>setBookmark</code></a>.</li>
    <li>Add an <a href="manual.html#eachLine"><code>eachLine</code></a>
    method to iterate over a document.</li>
    <li>New addon modules: <a href="../demo/markselection.html">selection
    marking</a>, <a href="../demo/lint.html">linting</a>,
    and <a href="../demo/closebrackets.html">automatic bracket
    closing</a>.</li>
    <li>Add <a href="manual.html#event_beforeChange"><code>"beforeChange"</code></a>
    and <a href="manual.html#event_beforeSelectionChange"><code>"beforeSelectionChange"</code></a>
    events.</li>
    <li>Add <a href="manual.html#event_hide"><code>"hide"</code></a>
    and <a href="manual.html#event_unhide"><code>"unhide"</code></a>
    events to marked ranges.</li>
    <li>Fix <a href="manual.html#coordsChar"><code>coordsChar</code></a>'s
    interpretation of its argument to match the documentation.</li>
    <li>New modes: <a href="../mode/turtle/index.html">Turtle</a>
    and <a href="../mode/q/index.html">Q</a>.</li>
    <li>Further improvements to the <a href="../demo/vim.html">vim mode</a>.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v3.01...v3.1">list of patches</a>.</li>
  </ul>


  <p class="rel">25-01-2013: <a href="https://codemirror.net/5/codemirror-3.02.zip">Version 3.02</a>:</p>

  <p class="rel-note">Single-bugfix release. Fixes a problem that
  prevents CodeMirror instances from being garbage-collected after
  they become unused.</p>

  <p class="rel">21-01-2013: <a href="https://codemirror.net/5/codemirror-3.01.zip">Version 3.01</a>:</p>

  <ul class="rel-note">
    <li>Move all add-ons into an organized directory structure
    under <a href="../addon/"><code>/addon</code></a>. <strong>You might have to adjust your
    paths.</strong></li>
    <li>New
    modes: <a href="../mode/d/index.html">D</a>, <a href="../mode/sass/index.html">Sass</a>, <a href="../mode/apl/index.html">APL</a>, <a href="../mode/sql/index.html">SQL</a>
    (configurable), and <a href="../mode/asterisk/index.html">Asterisk</a>.</li>
    <li>Several bugfixes in right-to-left text support.</li>
    <li>Add <a href="manual.html#option_rtlMoveVisually"><code>rtlMoveVisually</code></a> option.</li>
    <li>Improvements to vim keymap.</li>
    <li>Add built-in (lightweight) <a href="manual.html#addOverlay">overlay mode</a> support.</li>
    <li>Support <code>showIfHidden</code> option for <a href="manual.html#addLineWidget">line widgets</a>.</li>
    <li>Add simple <a href="manual.html#addon_python-hint">Python hinter</a>.</li>
    <li>Bring back the <a href="manual.html#option_fixedGutter"><code>fixedGutter</code></a> option.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v3.0...v3.01">list of patches</a>.</li>
  </ul>

  <p class="rel">10-12-2012: <a href="https://codemirror.net/5/codemirror-3.0.zip">Version 3.0</a>:</p>

  <p class="rel-note"><strong>New major version</strong>. Only
  partially backwards-compatible. See
  the <a href="upgrade_v3.html">upgrading guide</a> for more
  information. Changes since release candidate 2:</p>

  <ul class="rel-note">
    <li>Rewritten VIM mode.</li>
    <li>Fix a few minor scrolling and sizing issues.</li>
    <li>Work around Safari segfault when dragging.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v3.0rc2...v3.0">list of patches</a>.</li>
  </ul>

  <p class="rel">20-11-2012: <a href="https://codemirror.net/5/codemirror-3.0rc2.zip">Version 3.0, release candidate 2</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/http/index.html">HTTP</a>.</li>
    <li>Improved handling of selection anchor position.</li>
    <li>Improve IE performance on longer lines.</li>
    <li>Reduce gutter glitches during horiz. scrolling.</li>
    <li>Add <a href="manual.html#addKeyMap"><code>addKeyMap</code></a> and <a href="manual.html#removeKeyMap"><code>removeKeyMap</code></a> methods.</li>
    <li>Rewrite <code>formatting</code> and <code>closetag</code> add-ons.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v3.0rc1...v3.0rc2">list of patches</a>.</li>
  </ul>

  <p class="rel">20-11-2012: <a href="https://codemirror.net/5/codemirror-3.0rc1.zip">Version 3.0, release candidate 1</a>:</p>

  <ul class="rel-note">
    <li>New theme: <a href="../demo/theme.html#solarized%20light">Solarized</a>.</li>
    <li>Introduce <a href="manual.html#addLineClass"><code>addLineClass</code></a>
    and <a href="manual.html#removeLineClass"><code>removeLineClass</code></a>,
    drop <code>setLineClass</code>.</li>
    <li>Add a <em>lot</em> of
    new <a href="manual.html#markText">options for marked text</a>
    (read-only, atomic, collapsed, widget replacement).</li>
    <li>Remove the old code folding interface in favour of these new ranges.</li>
    <li>Add <a href="manual.html#isClean"><code>isClean</code></a>/<a href="manual.html#markClean"><code>markClean</code></a> methods.</li>
    <li>Remove <code>compoundChange</code> method, use better undo-event-combining heuristic.</li>
    <li>Improve scrolling performance smoothness.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v3.0beta2...v3.0rc1">list of patches</a>.</li>
  </ul>

  <p class="rel">22-10-2012: <a href="https://codemirror.net/5/codemirror-3.0beta2.zip">Version 3.0, beta 2</a>:</p>

  <ul class="rel-note">
    <li>Fix page-based coordinate computation.</li>
    <li>Fix firing of <a href="manual.html#event_gutterClick"><code>gutterClick</code></a> event.</li>
    <li>Add <a href="manual.html#option_cursorHeight"><code>cursorHeight</code></a> option.</li>
    <li>Fix bi-directional text regression.</li>
    <li>Add <a href="manual.html#option_viewportMargin"><code>viewportMargin</code></a> option.</li>
    <li>Directly handle mousewheel events (again, hopefully better).</li>
    <li>Make vertical cursor movement more robust (through widgets, big line gaps).</li>
    <li>Add <a href="manual.html#option_flattenSpans"><code>flattenSpans</code></a> option.</li>
    <li>Many optimizations. Poor responsiveness should be fixed.</li>
    <li>Initialization in hidden state works again.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v3.0beta1...v3.0beta2">list of patches</a>.</li>
  </ul>

  <p class="rel">19-09-2012: <a href="https://codemirror.net/5/codemirror-3.0beta1.zip">Version 3.0, beta 1</a>:</p>

  <ul class="rel-note">
    <li>Bi-directional text support.</li>
    <li>More powerful gutter model.</li>
    <li>Support for arbitrary text/widget height.</li>
    <li>In-line widgets.</li>
    <li>Generalized event handling.</li>
  </ul>

</section>

<section id=v2>

  <h2>Version 2.x</h2>

  <p class="rel">21-01-2013: <a href="https://codemirror.net/5/codemirror-2.38.zip">Version 2.38</a>:</p>

  <p class="rel-note">Integrate some bugfixes, enhancements to the vim keymap, and new
  modes
  (<a href="../mode/d/index.html">D</a>, <a href="../mode/sass/index.html">Sass</a>, <a href="../mode/apl/index.html">APL</a>)
  from the v3 branch.</p>

  <p class="rel">20-12-2012: <a href="https://codemirror.net/5/codemirror-2.37.zip">Version 2.37</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/sql/index.html">SQL</a> (will replace <a href="../mode/plsql/index.html">plsql</a> and <a href="../mode/mysql/index.html">mysql</a> modes).</li>
    <li>Further work on the new VIM mode.</li>
    <li>Fix Cmd/Ctrl keys on recent Operas on OS X.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v2.36...v2.37">list of patches</a>.</li>
  </ul>

  <p class="rel">20-11-2012: <a href="https://codemirror.net/5/codemirror-2.36.zip">Version 2.36</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/z80/index.html">Z80 assembly</a>.</li>
    <li>New theme: <a href="../demo/theme.html#twilight">Twilight</a>.</li>
    <li>Add command-line compression helper.</li>
    <li>Make <a href="manual.html#scrollIntoView"><code>scrollIntoView</code></a> public.</li>
    <li>Add <a href="manual.html#defaultTextHeight"><code>defaultTextHeight</code></a> method.</li>
    <li>Various extensions to the vim keymap.</li>
    <li>Make <a href="../mode/php/index.html">PHP mode</a> build on <a href="../mode/htmlmixed/index.html">mixed HTML mode</a>.</li>
    <li>Add <a href="manual.html#addon_continuecomment">comment-continuing</a> add-on.</li>
    <li>Full <a href="../https://github.com/codemirror/CodeMirror/compare/v2.35...v2.36">list of patches</a>.</li>
  </ul>

  <p class="rel">22-10-2012: <a href="https://codemirror.net/5/codemirror-2.35.zip">Version 2.35</a>:</p>

  <ul class="rel-note">
    <li>New (sub) mode: <a href="../mode/javascript/typescript.html">TypeScript</a>.</li>
    <li>Don't overwrite (insert key) when pasting.</li>
    <li>Fix several bugs in <a href="manual.html#markText"><code>markText</code></a>/undo interaction.</li>
    <li>Better indentation of JavaScript code without semicolons.</li>
    <li>Add <a href="manual.html#defineInitHook"><code>defineInitHook</code></a> function.</li>
    <li>Full <a href="https://github.com/codemirror/CodeMirror/compare/v2.34...v2.35">list of patches</a>.</li>
  </ul>

  <p class="rel">19-09-2012: <a href="https://codemirror.net/5/codemirror-2.34.zip">Version 2.34</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/commonlisp/index.html">Common Lisp</a>.</li>
    <li>Fix right-click select-all on most browsers.</li>
    <li>Change the way highlighting happens:<br>&nbsp; Saves memory and CPU cycles.<br>&nbsp; <code>compareStates</code> is no longer needed.<br>&nbsp; <code>onHighlightComplete</code> no longer works.</li>
    <li>Integrate mode (Markdown, XQuery, CSS, sTex) tests in central testsuite.</li>
    <li>Add a <a href="manual.html#version"><code>CodeMirror.version</code></a> property.</li>
    <li>More robust handling of nested modes in <a href="../demo/formatting.html">formatting</a> and <a href="../demo/closetag.html">closetag</a> plug-ins.</li>
    <li>Un/redo now preserves <a href="manual.html#markText">marked text</a> and bookmarks.</li>
    <li><a href="https://github.com/codemirror/CodeMirror/compare/v2.33...v2.34">Full list</a> of patches.</li>
  </ul>

  <p class="rel">23-08-2012: <a href="https://codemirror.net/5/codemirror-2.33.zip">Version 2.33</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/sieve/index.html">Sieve</a>.</li>
    <li>New <a href="manual.html#getViewport"><code>getViewPort</code></a> and <a href="manual.html#option_onViewportChange"><code>onViewportChange</code></a> API.</li>
    <li><a href="manual.html#option_cursorBlinkRate">Configurable</a> cursor blink rate.</li>
    <li>Make binding a key to <code>false</code> disabling handling (again).</li>
    <li>Show non-printing characters as red dots.</li>
    <li>More tweaks to the scrolling model.</li>
    <li>Expanded testsuite. Basic linter added.</li>
    <li>Remove most uses of <code>innerHTML</code>. Remove <code>CodeMirror.htmlEscape</code>.</li>
    <li><a href="https://github.com/codemirror/CodeMirror/compare/v2.32...v2.33">Full list</a> of patches.</li>
  </ul>

  <p class="rel">23-07-2012: <a href="https://codemirror.net/5/codemirror-2.32.zip">Version 2.32</a>:</p>

  <p class="rel-note">Emergency fix for a bug where an editor with
  line wrapping on IE will break when there is <em>no</em>
  scrollbar.</p>

  <p class="rel">20-07-2012: <a href="https://codemirror.net/5/codemirror-2.31.zip">Version 2.31</a>:</p>

  <ul class="rel-note">
    <li>New modes: <a href="../mode/ocaml/index.html">OCaml</a>, <a href="../mode/haxe/index.html">Haxe</a>, and <a href="../mode/vb/index.html">VB.NET</a>.</li>
    <li>Several fixes to the new scrolling model.</li>
    <li>Add a <a href="manual.html#setSize"><code>setSize</code></a> method for programmatic resizing.</li>
    <li>Add <a href="manual.html#getHistory"><code>getHistory</code></a> and <a href="manual.html#setHistory"><code>setHistory</code></a> methods.</li>
    <li>Allow custom line separator string in <a href="manual.html#getValue"><code>getValue</code></a> and <a href="manual.html#getRange"><code>getRange</code></a>.</li>
    <li>Support double- and triple-click drag, double-clicking whitespace.</li>
    <li>And more... <a href="https://github.com/codemirror/CodeMirror/compare/v2.3...v2.31">(all patches)</a></li>
  </ul>

  <p class="rel">22-06-2012: <a href="https://codemirror.net/5/codemirror-2.3.zip">Version 2.3</a>:</p>

  <ul class="rel-note">
    <li><strong>New scrollbar implementation</strong>. Should flicker less. Changes DOM structure of the editor.</li>
    <li>New theme: <a href="../demo/theme.html#vibrant-ink">vibrant-ink</a>.</li>
    <li>Many extensions to the VIM keymap (including text objects).</li>
    <li>Add <a href="../demo/multiplex.html">mode-multiplexing</a> utility script.</li>
    <li>Fix bug where right-click paste works in read-only mode.</li>
    <li>Add a <a href="manual.html#getScrollInfo"><code>getScrollInfo</code></a> method.</li>
    <li>Lots of other <a href="https://github.com/codemirror/CodeMirror/compare/v2.25...v2.3">fixes</a>.</li>
  </ul>

  <p class="rel">23-05-2012: <a href="https://codemirror.net/5/codemirror-2.25.zip">Version 2.25</a>:</p>

  <ul class="rel-note">
    <li>New mode: <a href="../mode/erlang/index.html">Erlang</a>.</li>
    <li><strong>Remove xmlpure mode</strong> (use <a href="../mode/xml/index.html">xml.js</a>).</li>
    <li>Fix line-wrapping in Opera.</li>
    <li>Fix X Windows middle-click paste in Chrome.</li>
    <li>Fix bug that broke pasting of huge documents.</li>
    <li>Fix backspace and tab key repeat in Opera.</li>
  </ul>

  <p class="rel">23-04-2012: <a href="https://codemirror.net/5/codemirror-2.24.zip">Version 2.24</a>:</p>

  <ul class="rel-note">
    <li><strong>Drop support for Internet Explorer 6</strong>.</li>
    <li>New
    modes: <a href="../mode/shell/index.html">Shell</a>, <a href="../mode/tiki/index.html">Tiki
    wiki</a>, <a href="../mode/pig/index.html">Pig Latin</a>.</li>
    <li>New themes: <a href="../demo/theme.html#ambiance">Ambiance</a>, <a href="../demo/theme.html#blackboard">Blackboard</a>.</li>
    <li>More control over drag/drop
    with <a href="manual.html#option_dragDrop"><code>dragDrop</code></a>
    and <a href="manual.html#option_onDragEvent"><code>onDragEvent</code></a>
    options.</li>
    <li>Make HTML mode a bit less pedantic.</li>
    <li>Add <a href="manual.html#compoundChange"><code>compoundChange</code></a> API method.</li>
    <li>Several fixes in undo history and line hiding.</li>
    <li>Remove (broken) support for <code>catchall</code> in key maps,
    add <code>nofallthrough</code> boolean field instead.</li>
  </ul>

  <p class="rel">26-03-2012: <a href="https://codemirror.net/5/codemirror-2.23.zip">Version 2.23</a>:</p>

  <ul class="rel-note">
    <li>Change <strong>default binding for tab</strong> <a href="javascript:void(document.getElementById('tabbinding').style.display='')">[more]</a>
      <div style="display: none" id=tabbinding>
        Starting in 2.23, these bindings are default:
        <ul><li>Tab: Insert tab character</li>
          <li>Shift-tab: Reset line indentation to default</li>
          <li>Ctrl/Cmd-[: Reduce line indentation (old tab behaviour)</li>
          <li>Ctrl/Cmd-]: Increase line indentation (old shift-tab behaviour)</li>
        </ul>
      </div>
    </li>
    <li>New modes: <a href="../mode/xquery/index.html">XQuery</a> and <a href="../mode/vbscript/index.html">VBScript</a>.</li>
    <li>Two new themes: <a href="../mode/less/index.html">lesser-dark</a> and <a href="../mode/xquery/index.html">xq-dark</a>.</li>
    <li>Differentiate between background and text styles in <a href="manual.html#setLineClass"><code>setLineClass</code></a>.</li>
    <li>Fix drag-and-drop in IE9+.</li>
    <li>Extend <a href="manual.html#charCoords"><code>charCoords</code></a>
    and <a href="manual.html#cursorCoords"><code>cursorCoords</code></a> with a <code>mode</code> argument.</li>
    <li>Add <a href="manual.html#option_autofocus"><code>autofocus</code></a> option.</li>
    <li>Add <a href="manual.html#findMarksAt"><code>findMarksAt</code></a> method.</li>
  </ul>

  <p class="rel">27-02-2012: <a href="https://codemirror.net/5/codemirror-2.22.zip">Version 2.22</a>:</p>

  <ul class="rel-note">
    <li>Allow <a href="manual.html#keymaps">key handlers</a> to pass up events, allow binding characters.</li>
    <li>Add <a href="manual.html#option_autoClearEmptyLines"><code>autoClearEmptyLines</code></a> option.</li>
    <li>Properly use tab stops when rendering tabs.</li>
    <li>Make PHP mode more robust.</li>
    <li>Support indentation blocks in <a href="manual.html#addon_foldcode">code folder</a>.</li>
    <li>Add a script for <a href="manual.html#addon_match-highlighter">highlighting instances of the selection</a>.</li>
    <li>New <a href="../mode/properties/index.html">.properties</a> mode.</li>
    <li>Fix many bugs.</li>
  </ul>

  <p class="rel">27-01-2012: <a href="https://codemirror.net/5/codemirror-2.21.zip">Version 2.21</a>:</p>

  <ul class="rel-note">
    <li>Added <a href="../mode/less/index.html">LESS</a>, <a href="../mode/mysql/index.html">MySQL</a>,
    <a href="../mode/go/index.html">Go</a>, and <a href="../mode/verilog/index.html">Verilog</a> modes.</li>
    <li>Add <a href="manual.html#option_smartIndent"><code>smartIndent</code></a>
    option.</li>
    <li>Support a cursor in <a href="manual.html#option_readOnly"><code>readOnly</code></a>-mode.</li>
    <li>Support assigning multiple styles to a token.</li>
    <li>Use a new approach to drawing the selection.</li>
    <li>Add <a href="manual.html#scrollTo"><code>scrollTo</code></a> method.</li>
    <li>Allow undo/redo events to span non-adjacent lines.</li>
    <li>Lots and lots of bugfixes.</li>
  </ul>

  <p class="rel">20-12-2011: <a href="https://codemirror.net/5/codemirror-2.2.zip">Version 2.2</a>:</p>

  <ul class="rel-note">
    <li>Slightly incompatible API changes. Read <a href="upgrade_v2.2.html">this</a>.</li>
    <li>New approach
    to <a href="manual.html#option_extraKeys">binding</a> keys,
    support for <a href="manual.html#option_keyMap">custom
    bindings</a>.</li>
    <li>Support for overwrite (insert).</li>
    <li><a href="manual.html#option_tabSize">Custom-width</a>
    and <a href="../demo/visibletabs.html">styleable</a> tabs.</li>
    <li>Moved more code into <a href="manual.html#addons">add-on scripts</a>.</li>
    <li>Support for sane vertical cursor movement in wrapped lines.</li>
    <li>More reliable handling of
    editing <a href="manual.html#markText">marked text</a>.</li>
    <li>Add minimal <a href="../demo/emacs.html">emacs</a>
    and <a href="../demo/vim.html">vim</a> bindings.</li>
    <li>Rename <code>coordsFromIndex</code>
    to <a href="manual.html#posFromIndex"><code>posFromIndex</code></a>,
    add <a href="manual.html#indexFromPos"><code>indexFromPos</code></a>
    method.</li>
  </ul>

  <p class="rel">21-11-2011: <a href="https://codemirror.net/5/codemirror-2.18.zip">Version 2.18</a>:</p>
  <p class="rel-note">Fixes <code>TextMarker.clear</code>, which is broken in 2.17.</p>

  <p class="rel">21-11-2011: <a href="https://codemirror.net/5/codemirror-2.17.zip">Version 2.17</a>:</p>
  <ul class="rel-note">
    <li>Add support for <a href="manual.html#option_lineWrapping">line
    wrapping</a> and <a href="manual.html#hideLine">code
    folding</a>.</li>
    <li>Add <a href="../mode/gfm/index.html">GitHub-style Markdown</a> mode.</li>
    <li>Add <a href="../theme/monokai.css">Monokai</a>
    and <a href="../theme/rubyblue.css">Rubyblue</a> themes.</li>
    <li>Add <a href="manual.html#setBookmark"><code>setBookmark</code></a> method.</li>
    <li>Move some of the demo code into reusable components
    under <a href="../addon/"><code>lib/util</code></a>.</li>
    <li>Make screen-coord-finding code faster and more reliable.</li>
    <li>Fix drag-and-drop in Firefox.</li>
    <li>Improve support for IME.</li>
    <li>Speed up content rendering.</li>
    <li>Fix browser's built-in search in Webkit.</li>
    <li>Make double- and triple-click work in IE.</li>
    <li>Various fixes to modes.</li>
  </ul>

  <p class="rel">27-10-2011: <a href="https://codemirror.net/5/codemirror-2.16.zip">Version 2.16</a>:</p>
  <ul class="rel-note">
    <li>Add <a href="../mode/perl/index.html">Perl</a>, <a href="../mode/rust/index.html">Rust</a>, <a href="../mode/tiddlywiki/index.html">TiddlyWiki</a>, and <a href="../mode/groovy/index.html">Groovy</a> modes.</li>
    <li>Dragging text inside the editor now moves, rather than copies.</li>
    <li>Add a <a href="manual.html#coordsFromIndex"><code>coordsFromIndex</code></a> method.</li>
    <li><strong>API change</strong>: <code>setValue</code> now no longer clears history. Use <a href="manual.html#clearHistory"><code>clearHistory</code></a> for that.</li>
    <li><strong>API change</strong>: <a href="manual.html#markText"><code>markText</code></a> now
    returns an object with <code>clear</code> and <code>find</code>
    methods. Marked text is now more robust when edited.</li>
    <li>Fix editing code with tabs in Internet Explorer.</li>
  </ul>

  <p class="rel">26-09-2011: <a href="https://codemirror.net/5/codemirror-2.15.zip">Version 2.15</a>:</p>
  <p class="rel-note">Fix bug that snuck into 2.14: Clicking the
  character that currently has the cursor didn't re-focus the
  editor.</p>

  <p class="rel">26-09-2011: <a href="https://codemirror.net/5/codemirror-2.14.zip">Version 2.14</a>:</p>
  <ul class="rel-note">
    <li>Add <a href="../mode/clojure/index.html">Clojure</a>, <a href="../mode/pascal/index.html">Pascal</a>, <a href="../mode/ntriples/index.html">NTriples</a>, <a href="../mode/jinja2/index.html">Jinja2</a>, and <a href="../mode/markdown/index.html">Markdown</a> modes.</li>
    <li>Add <a href="../theme/cobalt.css">Cobalt</a> and <a href="../theme/eclipse.css">Eclipse</a> themes.</li>
    <li>Add a <a href="manual.html#option_fixedGutter"><code>fixedGutter</code></a> option.</li>
    <li>Fix bug with <code>setValue</code> breaking cursor movement.</li>
    <li>Make gutter updates much more efficient.</li>
    <li>Allow dragging of text out of the editor (on modern browsers).</li>
  </ul>


  <p class="rel">23-08-2011: <a href="https://codemirror.net/5/codemirror-2.13.zip">Version 2.13</a>:</p>
  <ul class="rel-note">
    <li>Add <a href="../mode/ruby/index.html">Ruby</a>, <a href="../mode/r/index.html">R</a>, <a href="../mode/coffeescript/index.html">CoffeeScript</a>, and <a href="../mode/velocity/index.html">Velocity</a> modes.</li>
    <li>Add <a href="manual.html#getGutterElement"><code>getGutterElement</code></a> to API.</li>
    <li>Several fixes to scrolling and positioning.</li>
    <li>Add <a href="manual.html#option_smartHome"><code>smartHome</code></a> option.</li>
    <li>Add an experimental <a href="../mode/xmlpure/index.html">pure XML</a> mode.</li>
  </ul>

  <p class="rel">25-07-2011: <a href="https://codemirror.net/5/codemirror-2.12.zip">Version 2.12</a>:</p>
  <ul class="rel-note">
    <li>Add a <a href="../mode/sparql/index.html">SPARQL</a> mode.</li>
    <li>Fix bug with cursor jumping around in an unfocused editor in IE.</li>
    <li>Allow key and mouse events to bubble out of the editor. Ignore widget clicks.</li>
    <li>Solve cursor flakiness after undo/redo.</li>
    <li>Fix block-reindent ignoring the last few lines.</li>
    <li>Fix parsing of multi-line attrs in XML mode.</li>
    <li>Use <code>innerHTML</code> for HTML-escaping.</li>
    <li>Some fixes to indentation in C-like mode.</li>
    <li>Shrink horiz scrollbars when long lines removed.</li>
    <li>Fix width feedback loop bug that caused the width of an inner DIV to shrink.</li>
  </ul>

  <p class="rel">04-07-2011: <a href="https://codemirror.net/5/codemirror-2.11.zip">Version 2.11</a>:</p>
  <ul class="rel-note">
    <li>Add a <a href="../mode/scheme/index.html">Scheme mode</a>.</li>
    <li>Add a <code>replace</code> method to search cursors, for cursor-preserving replacements.</li>
    <li>Make the <a href="../mode/clike/index.html">C-like mode</a> mode more customizable.</li>
    <li>Update XML mode to spot mismatched tags.</li>
    <li>Add <code>getStateAfter</code> API and <code>compareState</code> mode API methods for finer-grained mode magic.</li>
    <li>Add a <code>getScrollerElement</code> API method to manipulate the scrolling DIV.</li>
    <li>Fix drag-and-drop for Firefox.</li>
    <li>Add a C# configuration for the <a href="../mode/clike/index.html">C-like mode</a>.</li>
    <li>Add <a href="../demo/fullscreen.html">full-screen editing</a> and <a href="../demo/changemode.html">mode-changing</a> demos.</li>
  </ul>

  <p class="rel">07-06-2011: <a href="https://codemirror.net/5/codemirror-2.1.zip">Version 2.1</a>:</p>
  <p class="rel-note">Add
  a <a href="manual.html#option_theme">theme</a> system
  (<a href="../demo/theme.html">demo</a>). Note that this is not
  backwards-compatible—you'll have to update your styles and
  modes!</p>

  <p class="rel">07-06-2011: <a href="https://codemirror.net/5/codemirror-2.02.zip">Version 2.02</a>:</p>
  <ul class="rel-note">
    <li>Add a <a href="../mode/lua/index.html">Lua mode</a>.</li>
    <li>Fix reverse-searching for a regexp.</li>
    <li>Empty lines can no longer break highlighting.</li>
    <li>Rework scrolling model (the outer wrapper no longer does the scrolling).</li>
    <li>Solve horizontal jittering on long lines.</li>
    <li>Add <a href="../demo/runmode.html">runmode.js</a>.</li>
    <li>Immediately re-highlight text when typing.</li>
    <li>Fix problem with 'sticking' horizontal scrollbar.</li>
  </ul>

  <p class="rel">26-05-2011: <a href="https://codemirror.net/5/codemirror-2.01.zip">Version 2.01</a>:</p>
  <ul class="rel-note">
    <li>Add a <a href="../mode/smalltalk/index.html">Smalltalk mode</a>.</li>
    <li>Add a <a href="../mode/rst/index.html">reStructuredText mode</a>.</li>
    <li>Add a <a href="../mode/python/index.html">Python mode</a>.</li>
    <li>Add a <a href="../mode/plsql/index.html">PL/SQL mode</a>.</li>
    <li><code>coordsChar</code> now works</li>
    <li>Fix a problem where <code>onCursorActivity</code> interfered with <code>onChange</code>.</li>
    <li>Fix a number of scrolling and mouse-click-position glitches.</li>
    <li>Pass information about the changed lines to <code>onChange</code>.</li>
    <li>Support cmd-up/down on OS X.</li>
    <li>Add triple-click line selection.</li>
    <li>Don't handle shift when changing the selection through the API.</li>
    <li>Support <code>"nocursor"</code> mode for <code>readOnly</code> option.</li>
    <li>Add an <code>onHighlightComplete</code> option.</li>
    <li>Fix the context menu for Firefox.</li>
  </ul>

  <p class="rel">28-03-2011: <a href="https://codemirror.net/5/codemirror-2.0.zip">Version 2.0</a>:</p>
  <p class="rel-note">CodeMirror 2 is a complete rewrite that's
  faster, smaller, simpler to use, and less dependent on browser
  quirks. See <a href="internals.html">this</a>
  and <a href="http://groups.google.com/group/codemirror/browse_thread/thread/5a8e894024a9f580">this</a>
  for more information.</p>

  <p class="rel">22-02-2011: <a href="https://github.com/codemirror/codemirror5/tree/beta2">Version 2.0 beta 2</a>:</p>
  <p class="rel-note">Somewhat more mature API, lots of bugs shaken out.</p>

  <p class="rel">17-02-2011: <a href="https://codemirror.net/5/codemirror-0.94.zip">Version 0.94</a>:</p>
  <ul class="rel-note">
    <li><code>tabMode: "spaces"</code> was modified slightly (now indents when something is selected).</li>
    <li>Fixes a bug that would cause the selection code to break on some IE versions.</li>
    <li>Disabling spell-check on WebKit browsers now works.</li>
  </ul>

  <p class="rel">08-02-2011: <a href="https://codemirror.net/5/">Version 2.0 beta 1</a>:</p>
  <p class="rel-note">CodeMirror 2 is a complete rewrite of
  CodeMirror, no longer depending on an editable frame.</p>

  <p class="rel">19-01-2011: <a href="https://codemirror.net/5/codemirror-0.93.zip">Version 0.93</a>:</p>
  <ul class="rel-note">
    <li>Added a <a href="contrib/regex/index.html">Regular Expression</a> parser.</li>
    <li>Fixes to the PHP parser.</li>
    <li>Support for regular expression in search/replace.</li>
    <li>Add <code>save</code> method to instances created with <code>fromTextArea</code>.</li>
    <li>Add support for MS T-SQL in the SQL parser.</li>
    <li>Support use of CSS classes for highlighting brackets.</li>
    <li>Fix yet another hang with line-numbering in hidden editors.</li>
  </ul>
</section>

<section id=v1>

  <h2>Version 0.x</h2>

  <p class="rel">28-03-2011: <a href="https://codemirror.net/5/codemirror-1.0.zip">Version 1.0</a>:</p>
  <ul class="rel-note">
    <li>Fix error when debug history overflows.</li>
    <li>Refine handling of C# verbatim strings.</li>
    <li>Fix some issues with JavaScript indentation.</li>
  </ul>

  <p class="rel">17-12-2010: <a href="https://codemirror.net/5/codemirror-0.92.zip">Version 0.92</a>:</p>
  <ul class="rel-note">
    <li>Make CodeMirror work in XHTML documents.</li>
    <li>Fix bug in handling of backslashes in Python strings.</li>
    <li>The <code>styleNumbers</code> option is now officially
    supported and documented.</li>
    <li><code>onLineNumberClick</code> option added.</li>
    <li>More consistent names <code>onLoad</code> and
    <code>onCursorActivity</code> callbacks. Old names still work, but
    are deprecated.</li>
    <li>Add a <a href="contrib/freemarker/index.html">Freemarker</a> mode.</li>
  </ul>

  <p class="rel">11-11-2010: <a
  href="https://codemirror.net/5/codemirror-0.91.zip">Version 0.91</a>:</p>
  <ul class="rel-note">
    <li>Adds support for <a href="contrib/java">Java</a>.</li>
    <li>Small additions to the <a href="contrib/php">PHP</a> and <a href="contrib/sql">SQL</a> parsers.</li>
    <li>Work around various <a href="https://bugs.webkit.org/show_bug.cgi?id=47806">Webkit</a> <a href="https://bugs.webkit.org/show_bug.cgi?id=23474">issues</a>.</li>
    <li>Fix <code>toTextArea</code> to update the code in the textarea.</li>
    <li>Add a <code>noScriptCaching</code> option (hack to ease development).</li>
    <li>Make sub-modes of <a href="mixedtest.html">HTML mixed</a> mode configurable.</li>
  </ul>

  <p class="rel">02-10-2010: <a
  href="https://codemirror.net/5/codemirror-0.9.zip">Version 0.9</a>:</p>
  <ul class="rel-note">
    <li>Add support for searching backwards.</li>
    <li>There are now parsers for <a href="contrib/scheme/index.html">Scheme</a>, <a href="contrib/xquery/index.html">XQuery</a>, and <a href="contrib/ometa/index.html">OmetaJS</a>.</li>
    <li>Makes <code>height: "dynamic"</code> more robust.</li>
    <li>Fixes bug where paste did not work on OS X.</li>
    <li>Add a <code>enterMode</code> and <code>electricChars</code> options to make indentation even more customizable.</li>
    <li>Add <code>firstLineNumber</code> option.</li>
    <li>Fix bad handling of <code>@media</code> rules by the CSS parser.</li>
    <li>Take a new, more robust approach to working around the invisible-last-line bug in WebKit.</li>
  </ul>

  <p class="rel">22-07-2010: <a
  href="https://codemirror.net/5/codemirror-0.8.zip">Version 0.8</a>:</p>
  <ul class="rel-note">
    <li>Add a <code>cursorCoords</code> method to find the screen
    coordinates of the cursor.</li>
    <li>A number of fixes and support for more syntax in the PHP parser.</li>
    <li>Fix indentation problem with JSON-mode JS parser in Webkit.</li>
    <li>Add a <a href="compress.html">minification</a> UI.</li>
    <li>Support a <code>height: dynamic</code> mode, where the editor's
    height will adjust to the size of its content.</li>
    <li>Better support for IME input mode.</li>
    <li>Fix JavaScript parser getting confused when seeing a no-argument
    function call.</li>
    <li>Have CSS parser see the difference between selectors and other
    identifiers.</li>
    <li>Fix scrolling bug when pasting in a horizontally-scrolled
    editor.</li>
    <li>Support <code>toTextArea</code> method in instances created with
    <code>fromTextArea</code>.</li>
    <li>Work around new Opera cursor bug that causes the cursor to jump
    when pressing backspace at the end of a line.</li>
  </ul>

  <p class="rel">27-04-2010: <a
  href="https://codemirror.net/5/codemirror-0.67.zip">Version
  0.67</a>:</p>
  <p class="rel-note">More consistent page-up/page-down behaviour
  across browsers. Fix some issues with hidden editors looping forever
  when line-numbers were enabled. Make PHP parser parse
  <code>"\\"</code> correctly. Have <code>jumpToLine</code> work on
  line handles, and add <code>cursorLine</code> function to fetch the
  line handle where the cursor currently is. Add new
  <code>setStylesheet</code> function to switch style-sheets in a
  running editor.</p>

  <p class="rel">01-03-2010: <a
  href="https://codemirror.net/5/codemirror-0.66.zip">Version
  0.66</a>:</p>
  <p class="rel-note">Adds <code>removeLine</code> method to API.
  Introduces the <a href="contrib/plsql/index.html">PLSQL parser</a>.
  Marks XML errors by adding (rather than replacing) a CSS class, so
  that they can be disabled by modifying their style. Fixes several
  selection bugs, and a number of small glitches.</p>

  <p class="rel">12-11-2009: <a
  href="https://codemirror.net/5/codemirror-0.65.zip">Version
  0.65</a>:</p>
  <p class="rel-note">Add support for having both line-wrapping and
  line-numbers turned on, make paren-highlighting style customisable
  (<code>markParen</code> and <code>unmarkParen</code> config
  options), work around a selection bug that Opera
  <em>re</em>introduced in version 10.</p>

  <p class="rel">23-10-2009: <a
  href="https://codemirror.net/5/codemirror-0.64.zip">Version
  0.64</a>:</p>
  <p class="rel-note">Solves some issues introduced by the
  paste-handling changes from the previous release. Adds
  <code>setSpellcheck</code>, <code>setTextWrapping</code>,
  <code>setIndentUnit</code>, <code>setUndoDepth</code>,
  <code>setTabMode</code>, and <code>setLineNumbers</code> to
  customise a running editor. Introduces an <a
  href="contrib/sql/index.html">SQL</a> parser. Fixes a few small
  problems in the <a href="contrib/python/index.html">Python</a>
  parser. And, as usual, add workarounds for various newly discovered
  browser incompatibilities.</p>

  <p class="rel">31-08-2009: <a href="https://codemirror.net/5/codemirror-0.63.zip">Version 0.63</a>:</p>
  <p class="rel-note"> Overhaul of paste-handling (less fragile), fixes for several
  serious IE8 issues (cursor jumping, end-of-document bugs) and a number
  of small problems.</p>

  <p class="rel">30-05-2009: <a href="https://codemirror.net/5/codemirror-0.62.zip">Version 0.62</a>:</p>
  <p class="rel-note">Introduces <a href="contrib/python/index.html">Python</a>
  and <a href="contrib/lua/index.html">Lua</a> parsers. Add
  <code>setParser</code> (on-the-fly mode changing) and
  <code>clearHistory</code> methods. Make parsing passes time-based
  instead of lines-based (see the <code>passTime</code> option).</p>

</section>
</article>

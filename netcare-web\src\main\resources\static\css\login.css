.form-group span.customerTip, .form-group .userTip, .form-group .pwdTip1, .form-group .pwdTip2, .form-group .isCheck, .newPassword1, .newPassword2, .coNewPassword1,.coNewwand1,.coNewwand2, .coNewPassword2,.validatecodeTip {
  color: red;
  display: none;
}
.submit {
  font-size: 18px;
  font-weight: 700;
  width: 300px !important;
  margin: 0 auto;
}

.agreement {
  cursor: pointer;
}

.placeholder {
  color: #999;
}

.input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}

form.m-t {
  margin: 10px auto;
}

.form-group {
  /*margin-bottom: 20px;*/
}

.form-group i {
  width: 40px;
  float: left;
  border: 1px solid #e5e6e7;
  border-right: none;
  padding-left: 0px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  height: 34px;
  line-height: 34px;
  background-color: #FFF;
}

input.form-control.col-sm-10 {
  width: 83%;
}

input.form-control {
  /*border-left: none;*/
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}

/*12-07*/
/*======= 表单 =======*/
.form-inline {
  position: relative;
  display: block;
}

.form-inline+.form-inline {
  margin-top: 10px;
}

.form-inline>div {
  height: 40px;
  line-height: 40px;
}

.form-inline-font {
  position: absolute;
  left: 0;
  width: 110px;
  padding-right: 10px;
  font-size: 15px;
  text-align: right;
  color: #000;
}

.form-inline-input input,
.code-box {
  width: 289px;
  height: 36px;
  color: #fff;
  text-shadow: 1px 1px 1px black;
  background: #ccc;
  border: 1px solid #e7e7e5;
  outline: none;
  border-radius: 3px;
  position: relative;
  /*box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.3), 0 1px rgba(255, 255, 255, 0.06);*/
}

.code-box p,
.code-box span {
  display: block;
  position: absolute;
  left: 0;
  height: 34px;
  text-align: center;
  line-height: 34px;
  border-radius: 3px;
}

.code-box p {
  text-shadow: none;
}

.code-box span {
  width: 34px;
  background-color: #fff;
  font-family: "宋体";
  font-size: 16px;
  color: #000;
  cursor: pointer;
  text-shadow: none;
}

.code-box p {
  width: 0;
  background-color: #FFFF99;
  overflow: hidden;
  text-indent: -20px;
  transition: background 1s ease-in;
}

.code-box .warnText {
  position: absolute;
  font-family: "宋体";
  width: 100%;
  height: 100%;
  text-align: center;
  line-height: 34px;
  color: #9a9791;
  text-shadow: none;
}

.code-box .code-input {
  display: none;
}

.login-btn {
  padding-right: 25px;
  padding-top: 15px;
  padding-bottom: 10px;
  text-align: right;
  font-size: 0;
}

.user-btn-link {
  display: inline-block;
  vertical-align: sub;
  padding-right: 10px;
}

.user-btn-link a {
  color: #fff;
  font-size:14px;
}

.user-btn-link a+a {
  margin-left: 5px;
}

.user-btn-link a:hover {
  text-decoration: underline;
}

.loginLeft {
  width: 650px;
  height: 540px;
}

.login-box {
  position: absolute;
  right: 90px;
  top: 150px;
  max-width: 400px;
  height: 400px;
  background-color: none;
}
.content {
  width: 100%;
  height: 700px;
  overflow: hidden;
}
.header {
  background-color: #fff;
  width: 100%;
  height: 80px;
  position: absolute;
  top: 0;
  border-bottom: 1px solid #ddd;
}
.btFooter {
  width: 100%;
  height: 80px;
  margin-top: -3%;
  border-top: 1px solid #dfdfdf;
}
.btFooter p {
  width: 100%;
  height: 20px;
  line-height: 20px;
  color: #ccc;
  padding-top: 10px;
  text-align: center;
}
.btFooter .icpgo {
  color: #23527c;
  cursor: pointer;
}
.btFooter .icpgo:hover {
  text-decoration: underline;
}
.header-left {
  width: 20%;
  height: 100%;
}
.header-left img {
  width: 95%;
  margin: 12px 0 0 80px;
}
.header-right {
  width: 500px;
  height: 100%;
  list-style: none;
}
.header-right li {
  float: left;
  padding: 0 20px;
  height: 80px;
  line-height: 80px;
  font-size: 16px
}
.header-right li i {
  font-style: normal;
  color: #f00;
}
.lang #language{
  display: inline-block;
  width:85px;
}
#banner {
  margin: 150px 0 0 7%;
  height: 400px;
  box-shadow:10px 10px 10px 10px #ccc;
}
#myDiv {
  width: 720px;
  height: 100%;
}
.slide01 {
  background: url(../img/sw1.png) no-repeat;
  background-size: cover;
}
.slide02 {
  background: url(../img/sw2.png) no-repeat;
  background-size: cover;
}
.slide03 {
  background: url(../img/sw3.png) no-repeat;
  background-size: cover;
}
.swiper-button-next {
  background-image: url(../img/toright.png) !important;
}
.swiper-button-prev {
  background-image: url(../img/toleft.png) !important;
}
.styleBG {
  background-color: #fff;
  height: auto;
}
#validatecode {
  width: 120px;
}


$(function () {
// http://localhost/static/neBusinessCont.html?ver=6.1.11&type=NE_SERVER_OR_NETWORK&neId=441403&businessId=network
// http://localhost/static/neBusinessCont.html?ver=6.1.11&type=NE_SERVER_OR_NETWORK&neId=446237&businessId=server
console.log('***********')

var token = Util.getSessionInfo("user").token;
	/*
	 * 页面元素根据角色权限筛选
	 */
	var str = parent.location.href;
	var strs = str.split("#");
	var href = parent.$("#side-menu  a[data-url='#" + strs[1] + "'] ", window.parent.document).attr("href");
	$.ajax({
		type: 'GET',
		url: Util.api + "/menuUrl/uielement?menuUrl=" + "./html/monitor/neBusinessNetWork.html?ver=6.1.33",
		dataType: "json",
		beforeSend: function(request) {
			request.setRequestHeader("Authorization", token);
		},
		success: function(json) {
			authrity = json;
			for(var i = 0; i < json.length; i++) {
				$("#" + json[i].elementRemark).css('display', 'inline-block');
			};
		}
	})

var searchString = Util.getSessionInfo("searchString");
// 通过去掉问号，将查询字符串转换为对象
var params = new URLSearchParams(searchString);

// 判断是否存在特定参数
if (params.has('type')) {
  var neId = params.get('neId');
  var businessId = params.get('businessId');
  var ciID=neId
if(businessId=='server'){
  var netServer ='服务器状态'
  Util.clearSessionStorage('searchString')
}else {
  var netServer ='网络状态'
  Util.clearSessionStorage('searchString')

}

} else {
  Util.clearSessionStorage('searchString')
  var ciID = Util.getSessionInfo("ciID");
  var netServer = Util.getSessionInfo("netServer");
}

	$.jgrid.defaults.styleUI = "Bootstrap";
	var customerID = Util.getSessionInfo("curSelectedCustom").customerID;

	var token = Util.getSessionInfo("user").token;
	var user = Util.getSessionInfo("user");
	var businessID;
  var subgrid_table_id="";
	//时间转换
	function add0(m) {
		return m < 10 ? '0' + m : m
	}

	function format(value) {
		//shijianchuo是整数，否则要parseInt转换
		var time = new Date(value);
		var y = time.getFullYear();
		var m = time.getMonth() + 1;
		var d = time.getDate();
		var h = time.getHours();
		var mm = time.getMinutes();
		var s = time.getSeconds();
		return y + '-' + add0(m) + '-' + add0(d) + ' ' + add0(h) + ':' + add0(mm) + ':' + add0(s);
	}
	$(".netServer").html(netServer);
	if (netServer == '服务器状态') {
		businessID = 'server';
	} else {
		businessID = 'network';
	}
	$.ajax({
		type: "get",
		url: Util.api + "/customer/" + customerID + "/ne/" + ciID + "/business/" + businessID + "/deviceBusiness",
		beforeSend: function (xhr) {
			xhr.setRequestHeader('Authorization', token);
		},
		async: true,
		success: function (res) {
			if (res.neName) {
				$(".marK").removeClass('hide')
				$(".deiverName").html(res.neName);
			} else if (res.rootNeName) {
				$(".marK").removeClass('hide')
				$(".deiverName").html(res.rootNeName);
			} else {
				$(".marK").addClass('hide')
			}
		}
	});

	if (user.language == 'zh') {
		// var colName = ["严重等级 ", "设备名称 ", "子网名称  ", "告警  ", "注释  ", "时间 ", "应答者 ", "应答时间 ", "ActionUserName"];
		var colName = ["严重等级", "子网名称", "告警", "详情", "告警时间"];
    var colName1 = ['severityName', 'serviceItem','stateName', 'updateTime','monitor',''];
		var colName2 = ["严重等级 ", "名称 ", "状态", "上一次状态改变","",""];
		// var colName3 = ["严重等级", "名称 ", "描述  ", "最后更新时间", ""];
    var treeName = ["严重等级", "名称 ", "描述  ", "最后更新时间", ""];
    var supervisoryName=['严重等级', '名称', '状态', '时间'];
    var snmpTrapsName=['','严重等级', 'Snmp traps','抑制', '永久抑制', '最后更新时间'];
		var captionEvent = "活跃告警";
    var protocolState="协议状态";
    var wait="智能等待禁用";
    var wait1="智能等待";
    var minutes="分钟";
	} else {
		// var colName = ["Severity level ", "Device name ", "Subnet name  ", "Warning", "Notes", "Time ", "Responder ", "Response time ", "ActionUserName"];
		var colName = ["Severity level", "Subnet name", "Warning", "Details", "Time"];
    var colName1 = ['severityName', 'serviceItem', 'stateName','updateTime','monitor',''];
		var colName2 = ["Severity level ", "Name ", "Describe", "Last state change","",""];
		// var colName3 = ["Severity level ", "Device name ", "Describe  ", "Last update time", ""];
    var supervisoryName=['name', 'Continue Time', 'State', 'Occur Time'];
    var snmpTrapsName=['','name', 'Snmp traps','restrain', 'Permanent inhibition', 'Last update time'];
		var captionEvent = "Active alarm";
    var protocolState="Protocol State";
    var wait="Smart Monitor Disabled";
    var wait1="Smart monitor";
    var minutes="minutes";
	}
	//活跃的网络告警
	$('#table_list_warn').jqGrid('clearGridData');
	$('#table_list_warn').jqGrid('setGridParam', {
		url: Util.api + "/customer/" + customerID + "/ne/" + ciID + "/eventHistory",
		loadBeforeSend: function (request) {
			request.setRequestHeader("Authorization", token);
		},
		jsonReader: {
			id: "eventID",
		}
	}).trigger('reloadGrid');
	$("#table_list_warn").jqGrid({
		url: Util.api + "/customer/" + customerID + "/ne/" + ciID + "/eventHistory",
		loadBeforeSend: function (request) {
			request.setRequestHeader("Authorization", token);
		},
    loadComplete:function(){
      $("#gbox_table_list_warn .ui-jqgrid-hdiv").addClass("my-grid-header3")
    },
		datatype: "json",
		height: "auto",
		caption: captionEvent,
		rowNum: 10,
		rowList: [10, 20, 50],
		pager: "#pager_list_2",
		jsonReader: {
			root: "content",
			records: "total",
			total: "pages",
			page: "pageNum",
			id: "ciID",
			repeatitems: false
		},
		autowidth: true,
		shrinkToFit: true,
		colNames: colName,
		rownumbers: true,
		colModel: [{
				name: "severityName",
				sortable: false,
				index: "severityName",
				formatter: function (value, row, index) {
					var colorNum = null;
					switch (value) {
						case "Unknown":
							colorNum = "#ccc";
							break;
            case "Monitoring":
              colorNum = "#6B95EB";
              break;
						case "Informational":
							colorNum = "#23c6c8";
							break;
						case "Warning":
							colorNum = "#D7B100";
							break;
						case "Minor":
							colorNum = "#f8ac95";
							break;
						case "Major":
							colorNum = "#d75e20";
							break;
						case "Critical":
							colorNum = "#CC0000";
							break;
						case "Normal":
							colorNum = "#3BB63A";
							break;
            case "Calculating":
              colorNum = "#bd9fd9";
              break;
					}

					var styleH = "color:#fff;border:none;display:block;min-width:70px;height:26px;line-height:26px;margin:0 auto;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
					var buttonHtml = "<button href='#' class='severityName' style='" + styleH + "'>" + value + "</button>";
					return buttonHtml;
				},
				width: 60
			},
			// {
			// 	name: "rootNeName",
			// 	index: "rootNeName",
			// 	width: 80,
			// 	sortable: false
			// },
			{
				name: "neName",
				index: "neName",
				sortable: false,
				width: 90
			}, {
				name: "eventValue",
				index: "eventValue",
				width: 150,
				sortable: false
			}, {
				name: "eventDesc",
				index: "eventDesc",
				sortable: false,
				width: 230,
				formatter: function(value, row, index) {
					return "<p class='' style='opacity: 0.8;word-wrap: break-word;white-space:pre-wrap;'>" + index.eventDesc + "</p>";
				}
			}, {
				name: "eventTime",
				index: "eventTime",
				formatter: "date",
				formatoptions: {
					srcformat: 'u',
					newformat: 'Y-m-d H:i:s'
				},
				width: 90,
				sortable: false
			}
			// ,
			// {
			// 	name: "actionUserName",
			// 	index: "actionUserName",
			// 	width: 80,
			// 	sortable: false
			// },
			// {
			// 	name: "actionTime",
			// 	index: "actionTime",
			// 	formatter: "date",
			// 	formatoptions: {
			// 		value: "0:'-';",
			// 		srcformat: 'u',
			// 		newformat: 'Y-m-d H:i:s',
			// 	},
			// 	width: 100,
			// 	formatter: function (value, row, index) {
			// 		if (value) {
			// 			return format(value);
			// 		} else {
			// 			return ''
			// 		}
			// 	},
			// 	sortable: false
			// }
		],
		viewrecords: true,
		hidegrid: false
	});

	//Service Item
	$('#table_list_2').jqGrid('clearGridData');
	$('#table_list_2').jqGrid('setGridParam', {
		url: Util.api + "/neServiceItem/" + ciID,
		loadBeforeSend: function (request) {
			request.setRequestHeader("Authorization", token);
		}
	}).trigger('reloadGrid');
	$('#table_list_2').jqGrid({
		url: Util.api + "/neServiceItem/" + ciID,
		loadBeforeSend: function (request) {
			request.setRequestHeader("Authorization", token);
		},
    loadComplete:function(){
      $(".ui-jqgrid-hdiv").addClass("my-grid-header")
    },
		datatype: "json",
		height: "auto",
		caption: "Icmp Polling",
		jsonReader: {
			root: "content",
			records: "total",
			total: "pages",
			page: "pageNum",
			id: "ciID",
			repeatitems: false
		},
		autowidth: true,
		shrinkToFit: true,
		// rowNum: 10,
		// rowList: [10, 20, 50],
		// pager: "#pager_list_1",
		colNames: colName2,
		rownumbers: true,
		colModel: [{
			name: "name",
			index: "name",
			sortable: false,
			formatter: function (value, row, index) {
				var colorNum = null;
				switch (value) {
					case "Unknown":
						colorNum = "#ccc";
						break;
          case "Monitoring":
            colorNum = "#6B95EB";
             break;
					case "Informational":
						colorNum = "#23c6c8";
						break;
					case "Warning":
						colorNum = "#D7B100";
						break;
					case "Minor":
						colorNum = "#f8ac95";
						break;
					case "Major":
						colorNum = "#d75e20";
						break;
					case "Critical":
						colorNum = "#CC0000";
						break;
					case "Normal":
						colorNum = "#3BB63A";
						break;
          case "Calculating":
            colorNum = "#bd9fd9";
            break;
				}
				var styleH = "color:#fff;border:none;padding-block:0px;display:block;min-width:70px;height:26px;line-height:26px;margin:0 auto;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
				var buttonHtml = "<button href='#' class='name' style='" + styleH + "'>" + value + "</button>";
				return buttonHtml;
			},
			width: 100
		}, {
			name: "serviceItem",
			index: "serviceItem",
			sortable: false,
			width: 150,
			sortable: false
		}, {
			name: "stateName",
			index: "stateName",
			sortable: false,
			width: 100
		}, {
			name: "updatedTime",
			index: "updatedTime",
			width: 150,
			formatter: "date",
			formatoptions: {
				srcformat: 'u',
				newformat: 'Y-m-d H:i:s'
			},
			sortable: false
    },
    {
      name: "monitor",
      index: "monitor",
      width: 100,
      formatter: function(value, row, index) {
          if (index.monitorTime==0 && index.monitor==true) {
          // return "<span style='color:#CCC'>智能等待禁用</span>";
          return `<span style='color:#CCC'>${wait}</span>`;
        } else if(index.monitor==true){
          // return "<span style='color:#3E97FF'>智能等待: "+ index.monitorTime +"分钟</span>";
          return `<span style='color:#3E97FF'>${wait1}: ${index.monitorTime}${minutes}</span>`;
        }else {
          return `<span style='color:#CCC'>${wait}</span>`;
        }
      },
      sortable: false
    },
    {
      name: "",
      index: "",
      width: 80,
      formatter:ConfigurationButton1
    }
  ],
		viewrecords: true,
		hidegrid: false
	});
//协议状态
$('#table_list_3').jqGrid('clearGridData');
$('#table_list_3').jqGrid('setGridParam', {
  url:Util.api + "/ciNeRoot/detail/" + ciID,
  loadBeforeSend: function (request) {
    request.setRequestHeader("Authorization", token);
  }
}).trigger('reloadGrid');
$("#table_list_3").jqGrid({
  url:Util.api + "/ciNeRoot/detail/" + ciID,
  loadBeforeSend: function (request) {
    request.setRequestHeader("Authorization", token);
  },
  loadComplete: function (data) {
    var filteredData = [];
    for (var i = 0; i < data.monitorVO.length; i++) {
        if (data.monitorVO[i].type == 2) {
            filteredData.push(data.monitorVO[i]);
        }
    }
    $(this).jqGrid('clearGridData', true);
    for (var j = 0; j < filteredData.length; j++) {
        $(this).jqGrid('addRowData', j + 1, filteredData[j]);
    }
},
  datatype: "json",
  height: "auto",
  caption: protocolState,
  jsonReader: {
    root: "monitorVO",
    records: "total",
    total: "pages",
    page: "pageNum",
    repeatitems: false
  },
  autowidth: true,
  shrinkToFit: true,
  // rowNum: 10,
  // rowList: [10, 20, 50],
  // pager: "#pager_list_1",
  colNames:supervisoryName,
  rownumbers: true,
  colModel: [{
    name: "severityName",
    invdate: "severityName",
    id: "severityName",
    width: 150,
    sortable: false,
    formatter: function (value, row, index) {
      var colorNum = null;
      switch (value) {
        case "Unknown":
          colorNum = "#ccc";
          break;
        case "Monitoring":
          colorNum = "#6B95EB";
          break;
        case "Informational":
          colorNum = "#23c6c8";
          break;
        case "Warning":
          colorNum = "#D7B100";
          break;
        case "Minor":
          colorNum = "#f8ac95";
          break;
        case "Major":
          colorNum = "#d75e20";
          break;
        case "Critical":
          colorNum = "#CC0000";
          break;
        case "Normal":
          colorNum = "#3BB63A";
          break;
        case "Calculating":
          colorNum = "#bd9fd9";
          break;
      }
      var styleH = "color:#fff;border:none;display:block;min-width:70px;height:26px;line-height:26px;margin:0 auto;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
      var buttonHtml = "<button href='#' class='interFaa' style='" + styleH + "'>" + value + "</button>";
      return buttonHtml;
    },
  }, {
    name: "name",
    invdate: "name",
    id: "name",
    width: 200,
    sortable: false
  }, {
    name: "severity",
    invdate: "severity",
    id: "severity",
    width: 200,
    sortable: false
  }, {
    name: "happenTime",
    invdate: "happenTime",
    id: "happenTime",
    width: 150,
    sortable: false,
    formatter: "date",
    formatoptions: {
      srcformat: 'u',
      newformat: 'Y-m-d H:i:s'
    },
  },
],
  viewrecords: true,
  hidegrid: false
});
//Snmp traps 抑制
$('#table_list_4').jqGrid('clearGridData');
$('#table_list_4').jqGrid('setGridParam', {
  url:Util.api + "/deviceBusiness/getLatestTrapList?rootNeId=" + ciID,
  loadBeforeSend: function (request) {
    request.setRequestHeader("Authorization", token);
  }
}).trigger('reloadGrid');
$("#table_list_4").jqGrid({
  url:Util.api + "/deviceBusiness/getLatestTrapList?rootNeId=" + ciID,
  loadBeforeSend: function (request) {
    request.setRequestHeader("Authorization", token);
  },
  loadComplete:function(data){
    if (data.data.length === 0) {
      $("#tableList4").hide();
    } else {
      $("#tableList4").show();
    }
    $("#gbox_table_list_4 .ui-jqgrid-hdiv").addClass("my-grid-header3")
  },
  datatype: "json",
  height: "auto",
  caption: "Snmp traps 抑制",
  jsonReader: {
    root: "data",
    records: "total",
    total: "pages",
    page: "pageNum",
    id:'trapSnmpId',
    repeatitems: false
  },
  autowidth: true,
  shrinkToFit: true,
  // rowNum: 10,
  // rowList: [10, 20, 50],
  // pager: "#pager_list_4",
  colNames:snmpTrapsName,
  rownumbers: true,
  colModel: [ {
    name: "trapSnmpId",
    invdate: "trapSnmpId",
    id: "trapSnmpId",
    sortable: false,
    hidden:true,
    width: 200,
  },{
    name: "severityName",
    invdate: "severityName",
    id: "severityName",
    sortable: false,
    width: 150,
    formatter: function (value, row, index) {
      var colorNum = null;
      switch (value) {
        case "Unknown":
          colorNum = "#ccc";
          break;
        case "Monitoring":
          colorNum = "#6B95EB";
          break;
        case "Informational":
          colorNum = "#23c6c8";
          break;
        case "Warning":
          colorNum = "#D7B100";
          break;
        case "Minor":
          colorNum = "#f8ac95";
          break;
        case "Major":
          colorNum = "#d75e20";
          break;
        case "Critical":
          colorNum = "#CC0000";
          break;
        case "Normal":
          colorNum = "#3BB63A";
          break;
        case "Calculating":
          colorNum = "#bd9fd9";
          break;
      }
      var styleH = "color:#fff;border:none;display:block;min-width:70px;height:26px;line-height:26px;margin:0 auto;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
      var buttonHtml = "<button href='#' class='interFaa' style='" + styleH + "'>" + value + "</button>";
      return buttonHtml;
    },
  }, {
    name: "profileName",
    invdate: "profileName",
    id: "profileName",
    sortable: false,
    width: 200,
  }, {
    name: "suppressing",
    invdate: "suppressing",
    id: "suppressing",
    sortable: false,
    width: 200,
    formatter: function (value, row, index) {
      if (value==1) {
        var status = true
        return `<input disabled class="" type='checkbox' checked/>`;
      } else {
        status = false
        return `<input disabled class="" type='checkbox' />`;
      }
    },
  },{
    name: "permanent",
    invdate: "permanent",
    id: "permanent",
    sortable: false,
    width: 200,
    formatter: function (value, row, cell) {
      if (value == 1) {
        var status = true
        return `<input class="" type='checkbox' checked onclick='isActive(${cell.trapSnmpId},${!status})'/>`;
      } else {
        status = false
        return `<input class="" type='checkbox' onclick='isActive(${cell.trapSnmpId},${!status})' />`;
      }
    },
  }, {
    name: "eventTime",
    invdate: "eventTime",
    id: "eventTime",
    sortable: false,
    width: 150,
    formatter: "date",
    formatoptions: {
      srcformat: 'u',
      newformat: 'Y-m-d H:i:s'
    },
  },
],
  viewrecords: true,
  hidegrid: false
});

	//点击close
	$(".close").click(function () {
		$("#warin").removeClass("blk in");
		$("#warin").removeClass("blk in");
	})
	//Interface
	var obj1 = {};
	obj1.customerID = customerID;
	obj1.rootNeID = ciID;
	if (netServer == "网络状态") {
		obj1.businessID = "network";
	} else if (netServer == "服务器状态") {
		obj1.businessID = "server";
	}
  // 中间动态部分
	$.ajax({
		type: "get",
		url: Util.api + "/deviceBusiness",
		beforeSend: function (xhr) {
			xhr.setRequestHeader('Authorization', token);
		},
		data: obj1,
		async: true,
		success: function (res) {
			if (res) {
				var data = [];
				var i = '';
				for (var key in res) {
					i = key;
					data = res[key];
					data.push({
						'param':'',
            "dynamicFieldList":[]
					})
          if (i.indexOf(" ") !== -1) {
            dataTab(i.replaceAll(" ", ""), i, data)
          } else {
            dataTab(i, i, data)
          }
				}
			}
			var neID;

			function dataTab(i, showName, data) {
        var newData=[]
        if (user.language == 'zh') {
          var colName3 = ["严重等级", "名称"];
        }else {
          var colName3 = ["Priority", "Name"];
        }
        var colModel3 = [
           {
           name: "severityName",
           sortable: false,
           index: "severityName",
           formatter: function (value, row, index) {
             var colorNum = null;
             switch (value) {
               case "Unknown":
                 colorNum = "#ccc";
                 break;
               case "Monitoring":
                 colorNum = "#6B95EB";
                 break;
               case "Informational":
                 colorNum = "#23c6c8";
                 break;
               case "Warning":
                 colorNum = "#D7B100";
                 break;
               case "Minor":
                 colorNum = "#f8ac95";
                 break;
               case "Major":
                 colorNum = "#d75e20";
                 break;
               case "Critical":
                 colorNum = "#CC0000";
                 break;
               case "Normal":
                 colorNum = "#3BB63A";
                 break;
								case "Calculating":
									colorNum = "#bd9fd9";
									break;
             }
             var styleH = "color:#fff;border:none;padding-block:0px;min-width:70px;height:26px;line-height:26px;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
             var buttonHtml = "<button href='#' class='interFaa' style='" + styleH + "'>" + value + "</button>";
             return buttonHtml;
           },
           width: 150
         }, {
           name: "neName",
           index: "neName",
           sortable: false,
           width: 200
         }];
         data[0].dynamicFieldList.forEach(function(element) {
          colName3.push(element.name);
                colModel3.push({
                  name:element.name,
                  index:element.name,
                  sortable:false,
                  width:100
                })
         })

         //colName增加最后两项

         if (user.language == 'zh') {
          colName3.push('最后更新时间');

           }else {
            colName3.push('Timestamp');
           }
         colName3.push(" ");
         //colModel 增加最后两项
         colModel3.push( {
          name: "latestUpdatedTime",
          index: "latestUpdatedTime",
          formatter: "date",
          formatoptions: {
            srcformat: 'u',
            newformat: 'Y-m-d H:i:s'
          },
          // width: 150,
          sortable: false
        })
        colModel3.push({
          name: "neID",
          index: "neID",
          hidden: true,
          key: true
        })
        data.forEach(function(item){
          var newItem={
            "severityName":item.severityName,
            "neName":item.neName,
            "alias":item.alias,
            "latestUpdatedTime":item.latestUpdatedTime,
            "neID":item.neID
          }
          item.dynamicFieldList.forEach(function(element){
            // 动态添加字段
            newItem[element.name]=element.value
          })
          newData.push(newItem)
        })

				var str = "<table id='table_list_" + i + "'></table>";
				$("#interFace").append(str);
				// var height = data.length*36;
				$('#table_list_' + i).jqGrid({
					data: newData,
					datatype: 'local',
					height: "auto",
					caption: showName,
					autowidth: true,
					shrinkToFit: true,
					rowNum: -1,
					colNames: colName3,
					rownumbers: true,
          colModel:colModel3,
					viewrecords: false,
					hidegrid: false,
          subGrid: true,
					subGridRowExpanded: function(subgrid_id, row_id) {
						UsersGroupID = row_id;
						var pager_id;
						subgrid_table_id = subgrid_id + "_t";//rolesManager_23_t
						pager_id = "p_" + subgrid_table_id;//p_rolesManager_23_t
            var subgridHeaderclass="#gbox_" + subgrid_table_id   //#gbox_table_list_Interface_443300_t
            //table
						$("#" + subgrid_id).html(
							"<table id='" + subgrid_table_id
								+ "' class='scroll'></table></div>"+
               "<div class='jqGrid_wrapper clearfix' id='tree_"+subgrid_id+"'></div>"
                );
						$("#" + subgrid_table_id).jqGrid({
							url:  Util.api + "/deviceBusiness/getNeServiceItemConfigList?neId="+row_id+"&businessId="+businessID,
							loadBeforeSend: function(request) {
								request.setRequestHeader("Authorization", token);
							},
							datatype: "json",
							height: "auto",
							autowidth: true,
							shrinkToFit: true,
							colNames: colName1,
							rowNum: 10,
							rowList: [10, 20, 50],
							rownumbers: true,
							colModel: [
								{
									name: "severityName",
									index: "severityName",
									width: 60,
									sortable: false,
									formatter: function (value, row, index) {
										var colorNum = null;
										switch (value) {
											case "Unknown":
												colorNum = "#ccc";
												break;
							              	case "Monitoring":
								                colorNum = "#6B95EB";
								                break;
											case "Informational":
												colorNum = "#23c6c8";
												break;
											case "Warning":
												colorNum = "#D7B100";
												break;
											case "Minor":
												colorNum = "#f8ac95";
												break;
											case "Major":
												colorNum = "#d75e20";
												break;
											case "Critical":
												colorNum = "#CC0000";
												break;
											case "Normal":
												colorNum = "#3BB63A";
												break;
                      case "Calculating":
                        colorNum = "#bd9fd9";
                        break;
										}

										var styleH = "color:#fff;border:none;display:block;min-width:70px;height:26px;line-height:26px;margin:0 auto;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
										var buttonHtml = "<button href='#' class='severityName' style='" + styleH + "'>" + value + "</button>";
										return buttonHtml;
									},
								}, {
									name: "serviceItem",
									index: "serviceItem",
									width: 80,
									sortable: false
								}, {
									name: "stateName",
									index: "stateName",
									width: 80,
									sortable: false,
									// hidden:true
								},
								{
									name: "updateTime",
									index: "updateTime",
									width: 100,
									formatter: "date",
									formatoptions: {
										srcformat: 'u',
										newformat: 'Y-m-d H:i:s'
									},
									sortable: false
								},{
									name: "monitor",
									index: "monitor",
									width: 60,
									formatter: function(value, row, index) {
											if (index.monitorTime==0 && index.monitor==true) {
                        return `<span style='color:#CCC'>${wait}</span>`;
										} else if(index.monitor==true){
                      // return "<span style='color:#3E97FF'>智能等待: "+ index.monitorTime +"分钟</span>";
                      return `<span style='color:#3E97FF'>${wait1}: ${index.monitorTime}${minutes}</span>`;
										}else {
                      return `<span style='color:#CCC'>${wait}</span>`;
                    }
									},
									sortable: false
								},
								{
									name: "",
									index: "",
									width: 50,
									formatter:ConfigurationButton
								}
							],
							viewrecords: true,
							jsonReader: {
								root: "data",
								records: "total",
								total: "pages",
								page: "pageNum",
								id: "usersID",
								repeatitems: false
							},
							hidegrid: false,
							loadComplete: function(res) {
							},
							pager : pager_id
						});
						jQuery("#" + subgrid_table_id).jqGrid('navGrid',
						  "#" + pager_id, {
							edit : false,
							add : false,
							del : false,
							search: false,
							refresh: false
						  });
               //动态添加类名
             $(subgridHeaderclass).addClass("my-grid-header1")

             bindSubGrid(subgrid_id, row_id,pager_id)

					},
					onSelectRow: function (id) {
						var rowData = $('#table_list_' + i).jqGrid("getRowData", id);
						neID = rowData.neID // = 2106;
						$('#table_list_warn').jqGrid('setGridParam', {
							url: Util.api + "/customer/" + customerID + "/ne/" + id + "/eventHistory",
							loadBeforeSend: function (request) {
								request.setRequestHeader("Authorization", token);
							},
							jsonReader: {
								id: "eventID",
							}
						}).trigger('reloadGrid');
					}
				});

			}
			//点击interFace
			$(document).on("click", ".interFaa", function () {
				$.ajax({
					type: "get",
					url: Util.api + "/deviceBusiness/" + neID,
					beforeSend: function (xhr) {
						xhr.setRequestHeader('Authorization', token);
					},
					async: true,
					success: function (res) {
						if (res) {
							$('#table_list_warn').jqGrid('clearGridData');
							$('#table_list_warn').jqGrid('setGridParam', {
								url: Util.api + "/customer/" + customerID + "/ne/" + neID + "/eventHistory",
								loadBeforeSend: function (request) {
									request.setRequestHeader("Authorization", token);
								},
								jsonReader: {
									id: "eventID",
								}
							}).trigger('reloadGrid');
						} else {
							toastr.info('该子网元下无相关数据');
						}
					}
				});
			})
		}
	});
  //第一层
  function bindSubGrid(subgrid_id, row_id,pager_id){
    $.ajax({
      type: "get",
      url: Util.api +  "/deviceBusiness/getNextNeServiceItemList?neId="+row_id+"&businessId="+businessID,
      beforeSend: function (xhr) {
        xhr.setRequestHeader('Authorization', token);
      },
      async: true,
      success: function (res) {
            var subi='';
            var subiName='';
            var subdata=[];
          if(res){
          res.data.forEach(function(item,index){
            subi=item.neTypeId.replace(/\s+/g, "")
            subiName=item.neTypeId
            subdata=item.nextNeList
            subdataTab(subgrid_id,subi,subdata)
          })
         }
         function subdataTab(subgrid_id,subi,subdata){
             var treenewData=[]
            //  var treecolName = ["严重等级", "名称"];
             if (user.language == 'zh') {
              var treecolName = ["严重等级", "名称"];
            }else {
              var treecolName = ["Priority", "Name"];
            }
             var treecolModel = [
           {
           name: "severityName",
           sortable: false,
           index: "severityName",
           formatter: function (value, row, index) {
             var colorNum = null;
             switch (value) {
               case "Unknown":
                 colorNum = "#ccc";
                 break;
               case "Monitoring":
                 colorNum = "#6B95EB";
                 break;
               case "Informational":
                 colorNum = "#23c6c8";
                 break;
               case "Warning":
                 colorNum = "#D7B100";
                 break;
               case "Minor":
                 colorNum = "#f8ac95";
                 break;
               case "Major":
                 colorNum = "#d75e20";
                 break;
               case "Critical":
                 colorNum = "#CC0000";
                 break;
               case "Normal":
                 colorNum = "#3BB63A";
                 break;
								case "Calculating":
									colorNum = "#bd9fd9";
									break;
             }
             var styleH = "color:#fff;border:none;min-width:70px;height:26px;line-height:26px;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
             var buttonHtml = "<button href='#' class='interFaa' style='" + styleH + "'>" + value + "</button>";
             return buttonHtml;
           },
           width: 150
         }, {
           name: "neName",
           index: "neName",
           sortable: false,
           width: 200
         }];
         subdata[0].dynamicFieldList.forEach(function(element) {
          // colName动态处理
          treecolName.push(element.name);
                // colModel动态处理
                treecolModel.push({
                  name:element.name,
                  index:element.name,
                  sortable:false,
                  width:100
                })
         })
        // treecolName.push('最后更新时间');
        if (user.language == 'zh') {
          treecolName.push('最后更新时间');

           }else {
            treecolName.push('Timestamp');
           }
         treecolName.push(" ");
         treecolModel.push( {
          name: "latestUpdatedTime",
          index: "latestUpdatedTime",
          formatter: "date",
          formatoptions: {
            srcformat: 'u',
            newformat: 'Y-m-d H:i:s'
          },
          // width: 150,
          sortable: false
        })
        treecolModel.push({
          name: "neID",
          index: "neID",
          hidden: true,
          key: true
        })
        subdata.forEach(function(item){
          var newItem={
            "severityName":item.severityName,
            "neName":item.neName,
            "alias":item.alias,
            "latestUpdatedTime":item.latestUpdatedTime,
            "neID":item.neID
          }
          item.dynamicFieldList.forEach(function(element){
            // 动态添加字段
            newItem[element.name]=element.value
          })
          treenewData.push(newItem)
        })
          var substr = "<table id='table_list_tree_" +subgrid_id+subi + "'></table>";
          	$("#tree_" + subgrid_id).append(substr);
          $('#table_list_tree_' + subgrid_id+subi).jqGrid({
            data: treenewData,
            datatype: "local",
            height: "auto",
            autowidth: true,
            shrinkToFit: true,
            caption:subiName,
            colNames: treecolName,
            rowNum: 10,
            rowList: [10, 20, 50],
            rownumbers: true,
            colModel: treecolModel,
            viewrecords: true,
            jsonReader: {
              root: "data",
              records: "total",
              total: "pages",
              page: "pageNum",
              id: "usersID",
              repeatitems: false
            },
            hidegrid: false,
            pager : pager_id,
            subGrid: true,
            subGridRowExpanded: function(subgrid_id1, row_id1) {
              UsersGroupID = row_id1;
              var pager_id1;
              subgrid_table_id1 = subgrid_id1 + "_t";
              pager_id1 = "p_" + subgrid_table_id1;
              var subgridHeaderclass1="#gbox_" + subgrid_table_id1
          $("#" + subgrid_id1).html(
            "<table id='" + subgrid_table_id1
              + "' class='scroll'></table></div>"+
            "<div class='jqGrid_wrapper clearfix' id='tree1_"+subgrid_id1+"'></div>"
              );
          $("#" + subgrid_table_id1).jqGrid({
            url:  Util.api + "/deviceBusiness/getNeServiceItemConfigList?neId="+row_id1+"&businessId="+businessID,
            loadBeforeSend: function(request) {
              request.setRequestHeader("Authorization", token);
            },
            datatype: "json",
            height: "auto",
            autowidth: true,
            shrinkToFit: true,
            colNames: colName1,
            rowNum: 10,
            rowList: [10, 20, 50],
            rownumbers: true,
            colModel: [
              {
                name: "severityName",
                index: "severityName",
                width: 60,
                sortable: false,
                formatter: function (value, row, index) {
                  var colorNum = null;
                  switch (value) {
                    case "Unknown":
                      colorNum = "#ccc";
                      break;
                            case "Monitoring":
                              colorNum = "#6B95EB";
                              break;
                    case "Informational":
                      colorNum = "#23c6c8";
                      break;
                    case "Warning":
                      colorNum = "#D7B100";
                      break;
                    case "Minor":
                      colorNum = "#f8ac95";
                      break;
                    case "Major":
                      colorNum = "#d75e20";
                      break;
                    case "Critical":
                      colorNum = "#CC0000";
                      break;
                    case "Normal":
                      colorNum = "#3BB63A";
                      break;
                    case "Calculating":
                      colorNum = "#bd9fd9";
                      break;
                  }

                  var styleH = "color:#fff;border:none;display:block;min-width:70px;height:26px;line-height:26px;margin:0 auto;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
                  var buttonHtml = "<button href='#' class='severityName' style='" + styleH + "'>" + value + "</button>";
                  return buttonHtml;
                },
              }, {
                name: "serviceItem",
                index: "serviceItem",
                width: 80,
                sortable: false
              }, {
                name: "stateName",
                index: "stateName",
                width: 80,
                sortable: false,
                // hidden:true
              },
              {
                name: "updateTime",
                index: "updateTime",
                width: 100,
                formatter: "date",
                formatoptions: {
                  srcformat: 'u',
                  newformat: 'Y-m-d H:i:s'
                },
                sortable: false
              },{
                name: "monitor",
                index: "monitor",
                width: 60,
                formatter: function(value, row, index) {
                    if (index.monitorTime==0 && index.monitor==true) {
                      return `<span style='color:#CCC'>${wait}</span>`;
                  } else if(index.monitor==true){
                    // return "<span style='color:#3E97FF'>智能等待: "+ index.monitorTime +"分钟</span>";
                    return `<span style='color:#3E97FF'>${wait1}: ${index.monitorTime}${minutes}</span>`;
                  }else {
                    return `<span style='color:#CCC'>${wait}</span>`;
                  }
                },
                sortable: false
              },
              {
                name: "",
                index: "",
                width: 50,
                formatter:ConfigurationButton
              }
            ],
            viewrecords: true,
            jsonReader: {
              root: "data",
              records: "total",
              total: "pages",
              page: "pageNum",
              id: "usersID",
              repeatitems: false
            },
            hidegrid: false,
            pager : pager_id
          });
          jQuery("#" + subgrid_table_id1).jqGrid('navGrid',
            "#" + pager_id, {
            edit : false,
            add : false,
            del : false,
            search: false,
            refresh: false
            });
             //动态添加类名
           $(subgridHeaderclass1).addClass("my-grid-header1")
             bindSubGrid1(subgrid_id1, row_id1,pager_id1)
            },
          });

         }


         }
    });

  }
  //第二层
  function bindSubGrid1(subgrid_id1, row_id1,pager_id1){
    $.ajax({
      type: "get",
      url: Util.api +  "/deviceBusiness/getNextNeServiceItemList?neId="+row_id1+"&businessId="+businessID,
      beforeSend: function (xhr) {
        xhr.setRequestHeader('Authorization', token);
      },
      async: true,
      success: function (res) {
            var subi1='';
            var subi1Name='';
            var subdata1=[];
          if(res){
          res.data.forEach(function(item,index){
            subi1=item.neTypeId.replace(/\s+/g, "")
            subi1Name=item.neTypeId
            subdata1=item.nextNeList
            subdataTab1(subgrid_id1,subi1,subdata1)
          })
          }
          function subdataTab1(subgrid_id1,subi1,subdata1){
              var treenewData1=[]
            //  var treecolName1 = ["严重等级", "名称"];
              if (user.language == 'zh') {
              var treecolName1 = ["严重等级", "名称"];
            }else {
              var treecolName1 = ["Priority", "Name"];
            }
              var treecolModel1 = [
            {
            name: "severityName",
            sortable: false,
            index: "severityName",
            formatter: function (value, row, index) {
              var colorNum = null;
              switch (value) {
                case "Unknown":
                  colorNum = "#ccc";
                  break;
                case "Monitoring":
                  colorNum = "#6B95EB";
                  break;
                case "Informational":
                  colorNum = "#23c6c8";
                  break;
                case "Warning":
                  colorNum = "#D7B100";
                  break;
                case "Minor":
                  colorNum = "#f8ac95";
                  break;
                case "Major":
                  colorNum = "#d75e20";
                  break;
                case "Critical":
                  colorNum = "#CC0000";
                  break;
                case "Normal":
                  colorNum = "#3BB63A";
                  break;
                case "Calculating":
                  colorNum = "#bd9fd9";
                  break;
              }
              var styleH = "color:#fff;border:none;padding-block:0px;min-width:70px;height:26px;line-height:26px;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
              var buttonHtml = "<button href='#' class='interFaa' style='" + styleH + "'>" + value + "</button>";
              return buttonHtml;
            },
            width: 150
          }, {
            name: "neName",
            index: "neName",
            sortable: false,
            width: 200
          }];
          subdata1[0].dynamicFieldList.forEach(function(element) {
          // colName动态处理
          treecolName1.push(element.name);
                // colModel动态处理
                treecolModel1.push({
                  name:element.name,
                  index:element.name,
                  sortable:false,
                  width:100
                })
          })
        // treecolName1.push('最后更新时间');
        if (user.language == 'zh') {
          treecolName1.push('最后更新时间');

            }else {
            treecolName1.push('Timestamp');
            }
          treecolName1.push(" ");
          treecolModel1.push( {
          name: "latestUpdatedTime",
          index: "latestUpdatedTime",
          formatter: "date",
          formatoptions: {
            srcformat: 'u',
            newformat: 'Y-m-d H:i:s'
          },
          // width: 150,
          sortable: false
        })
        treecolModel1.push({
          name: "neID",
          index: "neID",
          hidden: true,
          key: true
        })
        subdata1.forEach(function(item){
          var newItem={
            "severityName":item.severityName,
            "neName":item.neName,
            "alias":item.alias,
            "latestUpdatedTime":item.latestUpdatedTime,
            "neID":item.neID
          }
          item.dynamicFieldList.forEach(function(element){
            // 动态添加字段
            newItem[element.name]=element.value
          })
          treenewData1.push(newItem)
        })
          var substr1 = "<table id='table_list_tree1_" + subgrid_id1 + subi1+"'></table>";
            $("#tree1_" + subgrid_id1).append(substr1);
          $('#table_list_tree1_' + subgrid_id1+subi1).jqGrid({
            data: treenewData1,
            datatype: "local",
            height: "auto",
            autowidth: true,
            shrinkToFit: true,
            caption:subi1Name,
            colNames: treecolName1,
            rowNum: 10,
            rowList: [10, 20, 50],
            rownumbers: true,
            colModel: treecolModel1,
            viewrecords: true,
            hidegrid: false,
            pager : pager_id1,
            subGrid: true,
            subGridRowExpanded: function(subgrid_id2, row_id2) {
              UsersGroupID2 = row_id2;
              var pager_id2;
              subgrid_table_id2 = subgrid_id2 + "_t";
              pager_id2 = "p_" + subgrid_table_id2;
              var subgridHeaderclass2="#gbox_" + subgrid_table_id2
          $("#" + subgrid_id2).html(
            "<table id='" + subgrid_table_id2
              + "' class='scroll'></table></div>"+
            "<div class='jqGrid_wrapper clearfix' id='tree2_"+subgrid_id2+"'></div>"
              );
          $("#" + subgrid_table_id2).jqGrid({
            url:  Util.api + "/deviceBusiness/getNeServiceItemConfigList?neId="+row_id2+"&businessId="+businessID,
            loadBeforeSend: function(request) {
              request.setRequestHeader("Authorization", token);
            },
            datatype: "json",
            height: "auto",
            autowidth: true,
            shrinkToFit: true,
            colNames: colName1,
            rowNum: 10,
            rowList: [10, 20, 50],
            rownumbers: true,
            colModel: [
              {
                name: "severityName",
                index: "severityName",
                width: 60,
                sortable: false,
                formatter: function (value, row, index) {
                  var colorNum = null;
                  switch (value) {
                    case "Unknown":
                      colorNum = "#ccc";
                      break;
                            case "Monitoring":
                              colorNum = "#6B95EB";
                              break;
                    case "Informational":
                      colorNum = "#23c6c8";
                      break;
                    case "Warning":
                      colorNum = "#D7B100";
                      break;
                    case "Minor":
                      colorNum = "#f8ac95";
                      break;
                    case "Major":
                      colorNum = "#d75e20";
                      break;
                    case "Critical":
                      colorNum = "#CC0000";
                      break;
                    case "Normal":
                      colorNum = "#3BB63A";
                      break;
                    case "Calculating":
                      colorNum = "#bd9fd9";
                      break;
                  }

                  var styleH = "color:#fff;border:none;display:block;min-width:70px;height:26px;line-height:26px;margin:0 auto;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
                  var buttonHtml = "<button href='#' class='severityName' style='" + styleH + "'>" + value + "</button>";
                  return buttonHtml;
                },
              }, {
                name: "serviceItem",
                index: "serviceItem",
                width: 80,
                sortable: false
              }, {
                name: "stateName",
                index: "stateName",
                width: 80,
                sortable: false,
                // hidden:true
              },
              {
                name: "updateTime",
                index: "updateTime",
                width: 100,
                formatter: "date",
                formatoptions: {
                  srcformat: 'u',
                  newformat: 'Y-m-d H:i:s'
                },
                sortable: false
              },{
                name: "monitor",
                index: "monitor",
                width: 60,
                formatter: function(value, row, index) {
                    if (index.monitorTime==0 && index.monitor==true) {
                      return `<span style='color:#CCC'>${wait}</span>`;
                  } else if(index.monitor==true){
                    // return "<span style='color:#3E97FF'>智能等待: "+ index.monitorTime +"分钟</span>";
                    return `<span style='color:#3E97FF'>${wait1}: ${index.monitorTime}${minutes}</span>`;
                  }else {
                    return `<span style='color:#CCC'>${wait}</span>`;
                  }
                },
                sortable: false
              },
              {
                name: "",
                index: "",
                width: 50,
                formatter:ConfigurationButton
              }
            ],
            viewrecords: true,
            jsonReader: {
              root: "data",
              records: "total",
              total: "pages",
              page: "pageNum",
              id: "usersID",
              repeatitems: false
            },
            hidegrid: false,
            pager : pager_id2
          });
          jQuery("#" + subgrid_table_id2).jqGrid('navGrid',
            "#" + pager_id2, {
            edit : false,
            add : false,
            del : false,
            search: false,
            refresh: false
            });
              //动态添加类名
            $(subgridHeaderclass2).addClass("my-grid-header1")
              bindSubGrid2(subgrid_id2, row_id2,pager_id2)
            },
          });

          }


          }
    });

  }
  //第三层
  function bindSubGrid2(subgrid_id2, row_id2,pager_id2){
    $.ajax({
      type: "get",
      url: Util.api +  "/deviceBusiness/getNextNeServiceItemList?neId="+row_id2+"&businessId="+businessID,
      beforeSend: function (xhr) {
        xhr.setRequestHeader('Authorization', token);
      },
      async: true,
      success: function (res) {
        var subi2='';
        var subi2Name='';
        var subdata2=[];
        if(res){
          res.data.forEach(function(item,index){
            subi2=item.neTypeId.replace(/\s+/g, "")
            subi2Name=item.neTypeId
            subdata2=item.nextNeList
            subdataTab2(subgrid_id2,subi2,subdata2)
          })
        }
        function subdataTab2(subgrid_id2,subi2,subdata2){
            var treenewData2=[]
            // var treecolName2 = ["严重等级", "名称"];
            if (user.language == 'zh') {
              var treecolName2 = ["严重等级", "名称"];
            }else {
              var treecolName2 = ["Priority", "Name"];
            }
            var treecolModel2 = [
          {
          name: "severityName",
          sortable: false,
          index: "severityName",
          formatter: function (value, row, index) {
            var colorNum = null;
            switch (value) {
              case "Unknown":
                colorNum = "#ccc";
                break;
              case "Monitoring":
                colorNum = "#6B95EB";
                break;
              case "Informational":
                colorNum = "#23c6c8";
                break;
              case "Warning":
                colorNum = "#D7B100";
                break;
              case "Minor":
                colorNum = "#f8ac95";
                break;
              case "Major":
                colorNum = "#d75e20";
                break;
              case "Critical":
                colorNum = "#CC0000";
                break;
              case "Normal":
                colorNum = "#3BB63A";
                break;
              case "Calculating":
                colorNum = "#bd9fd9";
                break;
            }
            var styleH = "color:#fff;border:none;padding-block:0px;min-width:70px;height:26px;line-height:26px;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
            var buttonHtml = "<button href='#' class='interFaa' style='" + styleH + "'>" + value + "</button>";
            return buttonHtml;
          },
          width: 150
        }, {
          name: "neName",
          index: "neName",
          sortable: false,
          width: 200
        }];
        subdata2[0].dynamicFieldList.forEach(function(element) {
        // colName动态处理
        treecolName2.push(element.name);
              // colModel动态处理
              treecolModel2.push({
                name:element.name,
                index:element.name,
                sortable:false,
                width:100
              })
        })
        if (user.language == 'zh') {
        treecolName2.push('最后更新时间');

        }else {
          treecolName2.push('Timestamp');
        }
        treecolName2.push(" ");
        treecolModel2.push( {
        name: "latestUpdatedTime",
        index: "latestUpdatedTime",
        formatter: "date",
        formatoptions: {
          srcformat: 'u',
          newformat: 'Y-m-d H:i:s'
        },
        // width: 150,
        sortable: false
      })
      treecolModel2.push({
        name: "neID",
        index: "neID",
        hidden: true,
        key: true
      })
      subdata2.forEach(function(item){
        var newItem={
          "severityName":item.severityName,
          "neName":item.neName,
          "alias":item.alias,
          "latestUpdatedTime":item.latestUpdatedTime,
          "neID":item.neID
        }
        item.dynamicFieldList.forEach(function(element){
          // 动态添加字段
          newItem[element.name]=element.value
        })
        treenewData2.push(newItem)
      })
        var substr2 = "<table id='table_list_tree2_" + subgrid_id2 + subi2+"'></table>";
          $("#tree2_" + subgrid_id2).append(substr2);
        $('#table_list_tree2_' + subgrid_id2+subi2).jqGrid({
          data: treenewData2,
          datatype: "local",
          height: "auto",
          autowidth: true,
          shrinkToFit: true,
          caption:subi2Name,
          colNames: treecolName2,
          rowNum: 10,
          rowList: [10, 20, 50],
          rownumbers: true,
          colModel: treecolModel2,
          viewrecords: true,
          hidegrid: false,
          pager : pager_id2,
          subGrid: true,
          subGridRowExpanded: function(subgrid_id3, row_id3) {
            UsersGroupID3 = row_id3;
            var pager_id3;
            subgrid_table_id3 = subgrid_id3 + "_t";
            pager_id3 = "p_" + subgrid_table_id3;
            var subgridHeaderclass2="#gbox_" + subgrid_table_id3
        $("#" + subgrid_id3).html(
          "<table id='" + subgrid_table_id3
            + "' class='scroll'></table></div>"+
          "<div class='jqGrid_wrapper clearfix' id='tree2_"+subgrid_id3+"'></div>"
            );
        $("#" + subgrid_table_id3).jqGrid({
          url:  Util.api + "/deviceBusiness/getNeServiceItemConfigList?neId="+row_id3+"&businessId="+businessID,
          loadBeforeSend: function(request) {
            request.setRequestHeader("Authorization", token);
          },
          datatype: "json",
          height: "auto",
          autowidth: true,
          shrinkToFit: true,
          colNames: colName1,
          rowNum: 10,
          rowList: [10, 20, 50],
          rownumbers: true,
          colModel: [
            {
              name: "severityName",
              index: "severityName",
              width: 60,
              sortable: false,
              formatter: function (value, row, index) {
                var colorNum = null;
                switch (value) {
                  case "Unknown":
                    colorNum = "#ccc";
                    break;
                          case "Monitoring":
                            colorNum = "#6B95EB";
                            break;
                  case "Informational":
                    colorNum = "#23c6c8";
                    break;
                  case "Warning":
                    colorNum = "#D7B100";
                    break;
                  case "Minor":
                    colorNum = "#f8ac95";
                    break;
                  case "Major":
                    colorNum = "#d75e20";
                    break;
                  case "Critical":
                    colorNum = "#CC0000";
                    break;
                  case "Normal":
                    colorNum = "#3BB63A";
                    break;
                  case "Calculating":
                    colorNum = "#bd9fd9";
                    break;
                }

                var styleH = "color:#fff;border:none;display:block;min-width:70px;height:26px;line-height:26px;margin:0 auto;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
                var buttonHtml = "<button href='#' class='severityName' style='" + styleH + "'>" + value + "</button>";
                return buttonHtml;
              },
            }, {
              name: "serviceItem",
              index: "serviceItem",
              width: 80,
              sortable: false
            }, {
              name: "stateName",
              index: "stateName",
              width: 80,
              sortable: false,
              // hidden:true
            },
            {
              name: "updateTime",
              index: "updateTime",
              width: 100,
              formatter: "date",
              formatoptions: {
                srcformat: 'u',
                newformat: 'Y-m-d H:i:s'
              },
              sortable: false
            },{
              name: "monitor",
              index: "monitor",
              width: 60,
              formatter: function(value, row, index) {
                  if (index.monitorTime==0 && index.monitor==true) {
                    return `<span style='color:#CCC'>${wait}</span>`;
                } else if(index.monitor==true){
                  // return "<span style='color:#3E97FF'>智能等待: "+ index.monitorTime +"分钟</span>";
                  return `<span style='color:#3E97FF'>${wait1}: ${index.monitorTime}${minutes}</span>`;
                }else {
                  return `<span style='color:#CCC'>${wait}</span>`;
                }
              },
              sortable: false
            },
            {
              name: "",
              index: "",
              width: 50,
              formatter:ConfigurationButton
            }
          ],
          viewrecords: true,
          jsonReader: {
            root: "data",
            records: "total",
            total: "pages",
            page: "pageNum",
            id: "usersID",
            repeatitems: false
          },
          hidegrid: false,
          pager : pager_id3
        });
        jQuery("#" + subgrid_table_id3).jqGrid('navGrid',
          "#" + pager_id3, {
          edit : false,
          add : false,
          del : false,
          search: false,
          refresh: false
          });
            //动态添加类名
          $(subgridHeaderclass2).addClass("my-grid-header1")
            bindSubGrid3(subgrid_id3, row_id3,pager_id3)
          },
        });

        }
      }
    });

  }
  //第四层
  function bindSubGrid3(subgrid_id3, row_id3,pager_id3){
    $.ajax({
      type: "get",
      url: Util.api +  "/deviceBusiness/getNextNeServiceItemList?neId="+row_id3+"&businessId="+businessID,
      beforeSend: function (xhr) {
        xhr.setRequestHeader('Authorization', token);
      },
      async: true,
      success: function (res) {
        var subi2='';
        var subi2Name='';
        var subdata2=[];
        if(res){
          res.data.forEach(function(item,index){
            subi2=item.neTypeId.replace(/\s+/g, "")
            subi2Name=item.neTypeId
            subdata2=item.nextNeList
            subdataTab2(subgrid_id3,subi2,subdata2)
          })
        }
        function subdataTab2(subgrid_id3,subi2,subdata2){
            var treenewData2=[]
            // var treecolName2 = ["严重等级", "名称"];
            if (user.language == 'zh') {
              var treecolName2 = ["严重等级", "名称"];
            }else {
              var treecolName2 = ["Priority", "Name"];
            }
            var treecolModel2 = [
          {
          name: "severityName",
          sortable: false,
          index: "severityName",
          formatter: function (value, row, index) {
            var colorNum = null;
            switch (value) {
              case "Unknown":
                colorNum = "#ccc";
                break;
              case "Monitoring":
                colorNum = "#6B95EB";
                break;
              case "Informational":
                colorNum = "#23c6c8";
                break;
              case "Warning":
                colorNum = "#D7B100";
                break;
              case "Minor":
                colorNum = "#f8ac95";
                break;
              case "Major":
                colorNum = "#d75e20";
                break;
              case "Critical":
                colorNum = "#CC0000";
                break;
              case "Normal":
                colorNum = "#3BB63A";
                break;
              case "Calculating":
                colorNum = "#bd9fd9";
                break;
            }
            var styleH = "color:#fff;border:none;padding-block:0px;min-width:70px;height:26px;line-height:26px;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
            var buttonHtml = "<button href='#' class='interFaa' style='" + styleH + "'>" + value + "</button>";
            return buttonHtml;
          },
          width: 150
        }, {
          name: "neName",
          index: "neName",
          sortable: false,
          width: 200
        }];
        subdata2[0].dynamicFieldList.forEach(function(element) {
        // colName动态处理
        treecolName2.push(element.name);
              // colModel动态处理
              treecolModel2.push({
                name:element.name,
                index:element.name,
                sortable:false,
                width:100
              })
        })
        if (user.language == 'zh') {
        treecolName2.push('最后更新时间');

        }else {
          treecolName2.push('Timestamp');
        }
        treecolName2.push(" ");
        treecolModel2.push( {
        name: "latestUpdatedTime",
        index: "latestUpdatedTime",
        formatter: "date",
        formatoptions: {
          srcformat: 'u',
          newformat: 'Y-m-d H:i:s'
        },
        // width: 150,
        sortable: false
      })
      treecolModel2.push({
        name: "neID",
        index: "neID",
        hidden: true,
        key: true
      })
      subdata2.forEach(function(item){
        var newItem={
          "severityName":item.severityName,
          "neName":item.neName,
          "alias":item.alias,
          "latestUpdatedTime":item.latestUpdatedTime,
          "neID":item.neID
        }
        item.dynamicFieldList.forEach(function(element){
          // 动态添加字段
          newItem[element.name]=element.value
        })
        treenewData2.push(newItem)
      })
        var substr2 = "<table id='table_list_tree2_" + subgrid_id3 + subi2+"'></table>";
          $("#tree2_" + subgrid_id3).append(substr2);
        $('#table_list_tree2_' + subgrid_id3+subi2).jqGrid({
          data: treenewData2,
          datatype: "local",
          height: "auto",
          autowidth: true,
          shrinkToFit: true,
          caption:subi2Name,
          colNames: treecolName2,
          rowNum: 10,
          rowList: [10, 20, 50],
          rownumbers: true,
          colModel: treecolModel2,
          viewrecords: true,
          hidegrid: false,
          pager : pager_id3,
          subGrid: true,
          subGridRowExpanded: function(subgrid_id4, row_id4) {
            UsersGroupID4 = row_id4;
            var pager_id4;
            subgrid_table_id4 = subgrid_id4 + "_t";
            pager_id4 = "p_" + subgrid_table_id4;
            var subgridHeaderclass2="#gbox_" + subgrid_table_id4
        $("#" + subgrid_id4).html(
          "<table id='" + subgrid_table_id4
            + "' class='scroll'></table></div>"+
          "<div class='jqGrid_wrapper clearfix' id='tree2_"+subgrid_id4+"'></div>"
            );
        $("#" + subgrid_table_id4).jqGrid({
          url:  Util.api + "/deviceBusiness/getNeServiceItemConfigList?neId="+row_id4+"&businessId="+businessID,
          loadBeforeSend: function(request) {
            request.setRequestHeader("Authorization", token);
          },
          datatype: "json",
          height: "auto",
          autowidth: true,
          shrinkToFit: true,
          colNames: colName1,
          rowNum: 10,
          rowList: [10, 20, 50],
          rownumbers: true,
          colModel: [
            {
              name: "severityName",
              index: "severityName",
              width: 60,
              sortable: false,
              formatter: function (value, row, index) {
                var colorNum = null;
                switch (value) {
                  case "Unknown":
                    colorNum = "#ccc";
                    break;
                          case "Monitoring":
                            colorNum = "#6B95EB";
                            break;
                  case "Informational":
                    colorNum = "#23c6c8";
                    break;
                  case "Warning":
                    colorNum = "#D7B100";
                    break;
                  case "Minor":
                    colorNum = "#f8ac95";
                    break;
                  case "Major":
                    colorNum = "#d75e20";
                    break;
                  case "Critical":
                    colorNum = "#CC0000";
                    break;
                  case "Normal":
                    colorNum = "#3BB63A";
                    break;
                  case "Calculating":
                    colorNum = "#bd9fd9";
                    break;
                }

                var styleH = "color:#fff;border:none;display:block;min-width:70px;height:26px;line-height:26px;margin:0 auto;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
                var buttonHtml = "<button href='#' class='severityName' style='" + styleH + "'>" + value + "</button>";
                return buttonHtml;
              },
            }, {
              name: "serviceItem",
              index: "serviceItem",
              width: 80,
              sortable: false
            }, {
              name: "stateName",
              index: "stateName",
              width: 80,
              sortable: false,
              // hidden:true
            },
            {
              name: "updateTime",
              index: "updateTime",
              width: 100,
              formatter: "date",
              formatoptions: {
                srcformat: 'u',
                newformat: 'Y-m-d H:i:s'
              },
              sortable: false
            },{
              name: "monitor",
              index: "monitor",
              width: 60,
              formatter: function(value, row, index) {
                  if (index.monitorTime==0 && index.monitor==true) {
                    return `<span style='color:#CCC'>${wait}</span>`;
                } else if(index.monitor==true){
                  // return "<span style='color:#3E97FF'>智能等待: "+ index.monitorTime +"分钟</span>";
                  return `<span style='color:#3E97FF'>${wait1}: ${index.monitorTime}${minutes}</span>`;
                }else {
                  return `<span style='color:#CCC'>${wait}</span>`;
                }
              },
              sortable: false
            },
            {
              name: "",
              index: "",
              width: 50,
              formatter:ConfigurationButton
            }
          ],
          viewrecords: true,
          jsonReader: {
            root: "data",
            records: "total",
            total: "pages",
            page: "pageNum",
            id: "usersID",
            repeatitems: false
          },
          hidegrid: false,
          pager : pager_id4
        });
        jQuery("#" + subgrid_table_id4).jqGrid('navGrid',
          "#" + pager_id4, {
          edit : false,
          add : false,
          del : false,
          search: false,
          refresh: false
          });
            //动态添加类名
          $(subgridHeaderclass2).addClass("my-grid-header1")
            bindSubGrid4(subgrid_id4, row_id4,pager_id4)
          },
        });

        }
      }
    });

  }
    //第五层
  function bindSubGrid4(subgrid_id4, row_id4,pager_id4){
    $.ajax({
      type: "get",
      url: Util.api +  "/deviceBusiness/getNextNeServiceItemList?neId="+row_id4+"&businessId="+businessID,
      beforeSend: function (xhr) {
        xhr.setRequestHeader('Authorization', token);
      },
      async: true,
      success: function (res) {
        var subi2='';
        var subi2Name='';
        var subdata2=[];
        if(res){
          res.data.forEach(function(item,index){
            subi2=item.neTypeId.replace(/\s+/g, "")
            subi2Name=item.neTypeId
            subdata2=item.nextNeList
            subdataTab2(subgrid_id4,subi2,subdata2)
          })
        }
        function subdataTab2(subgrid_id4,subi2,subdata2){
            var treenewData2=[]
            // var treecolName2 = ["严重等级", "名称"];
            if (user.language == 'zh') {
              var treecolName2 = ["严重等级", "名称"];
            }else {
              var treecolName2 = ["Priority", "Name"];
            }
            var treecolModel2 = [
          {
          name: "severityName",
          sortable: false,
          index: "severityName",
          formatter: function (value, row, index) {
            var colorNum = null;
            switch (value) {
              case "Unknown":
                colorNum = "#ccc";
                break;
              case "Monitoring":
                colorNum = "#6B95EB";
                break;
              case "Informational":
                colorNum = "#23c6c8";
                break;
              case "Warning":
                colorNum = "#D7B100";
                break;
              case "Minor":
                colorNum = "#f8ac95";
                break;
              case "Major":
                colorNum = "#d75e20";
                break;
              case "Critical":
                colorNum = "#CC0000";
                break;
              case "Normal":
                colorNum = "#3BB63A";
                break;
              case "Calculating":
                colorNum = "#bd9fd9";
                break;
            }
            var styleH = "color:#fff;border:none;padding-block:0px;min-width:70px;height:26px;line-height:26px;outline:none;-moz-border-radius:10px;-ms-border-radius:10px;-o-border-radius:10px;border-radius:10px;background:" + colorNum;
            var buttonHtml = "<button href='#' class='interFaa' style='" + styleH + "'>" + value + "</button>";
            return buttonHtml;
          },
          width: 150
        }, {
          name: "neName",
          index: "neName",
          sortable: false,
          width: 200
        }];
        subdata2[0].dynamicFieldList.forEach(function(element) {
        // colName动态处理
        treecolName2.push(element.name);
              // colModel动态处理
              treecolModel2.push({
                name:element.name,
                index:element.name,
                sortable:false,
                width:100
              })
        })
        if (user.language == 'zh') {
        treecolName2.push('最后更新时间');

        }else {
          treecolName2.push('Timestamp');
        }
        treecolName2.push(" ");
        treecolModel2.push( {
        name: "latestUpdatedTime",
        index: "latestUpdatedTime",
        formatter: "date",
        formatoptions: {
          srcformat: 'u',
          newformat: 'Y-m-d H:i:s'
        },
        // width: 150,
        sortable: false
      })
      treecolModel2.push({
        name: "neID",
        index: "neID",
        hidden: true,
        key: true
      })
      subdata2.forEach(function(item){
        var newItem={
          "severityName":item.severityName,
          "neName":item.neName,
          "alias":item.alias,
          "latestUpdatedTime":item.latestUpdatedTime,
          "neID":item.neID
        }
        item.dynamicFieldList.forEach(function(element){
          // 动态添加字段
          newItem[element.name]=element.value
        })
        treenewData2.push(newItem)
      })
        var substr2 = "<table id='table_list_tree2_" + subgrid_id4 + subi2+"'></table>";
          $("#tree2_" + subgrid_id4).append(substr2);
        $('#table_list_tree2_' + subgrid_id4+subi2).jqGrid({
          data: treenewData2,
          datatype: "local",
          height: "auto",
          autowidth: true,
          shrinkToFit: true,
          caption:subi2Name,
          colNames: treecolName2,
          rowNum: 10,
          rowList: [10, 20, 50],
          rownumbers: true,
          colModel: treecolModel2,
          viewrecords: true,
          hidegrid: false,
          pager : pager_id4,
        });

        }
      }
    });

  }
	//点击deiverName
	$(".deiverName").click(function () {
		if (netServer == "网络状态") {
			window.open(Util.jumpApi + "/static/index.html#neBusinessNetWork?ver=" + getVersions());
		} else if (netServer == "服务器状态") {
			window.open(Util.jumpApi + "/static/index.html#neBusinessServer?ver=" + getVersions());
		}
	});
  // 功能配置数据-------------------Start-------------------------------------
	var _levels = [
		{ level: 1, percent: "", severity: "Warning" },
		{ level: 2, percent: "", severity: "Major" },
		{ level: 3, percent: "", severity: "Critical" }
		];
	var _options =["Warning", "Minor", "Major", "Critical"];
	var _durations = ["直接模式", "5分", "10分", "15分", "30分", "45分", "60分"];
  // 功能配置表单生成
function generateConf(dom, type = 0) {
	var htmlString = type
	  ? '<legend><label><input name="coverFlag" type="checkbox"> 覆盖阈值默认值</label></legend>'
	  : "<legend>默认阈值配置</legend>";

	htmlString += `
		<div class="form-group">
		  <label class="col-sm-2 control-label"></label>
		  <div class="col-sm-5">Percent</div>
		  <div class="col-sm-5">严重等级</div>
		</div>
	  `;

	_levels.forEach(function (level) {
	  htmlString += `
			<div class="form-group">
				<label class="col-sm-2 control-label">Level ${level.level}</label>
				<div class="col-sm-5">
					<input ${type ? "" : "disabled"} name="${
		(type ? "set_" : "") + "race" + level.level
	  }" type="text" class="form-control" placeholder="请输入数值">
				</div>
				<div class="col-sm-5">
					<select ${type ? "" : "disabled"} name="${
		(type ? "set_" : "") + "alarm" + level.level
	  }" class="form-control">
						${_options
						  .map(
							(option) =>
							  `<option value="${option}">${option}</option>`
						  )
						  .join("")}
					</select>
				</div>
			</div>
			`;
	});

	htmlString += `
		<div class="form-group">
			<label class="col-sm-2 control-label"></label>
			<div class="col-sm-5">
				<label class="col-sm-2 control-label">持续时间</label>
			</div>
			<div class="col-sm-5">
				<select ${type ? "" : "disabled"} name="${
	  type ? "set_" : ""
	}Duration" class="form-control">
					${_durations
					  .map(
						(duration, i) =>
						  `<option value="${
							duration === "直接模式"
							  ? i
							  : duration.replace("分", "")
						  }">${duration}</option>`
					  )
					  .join("")}
				</select>
			</div>
		</div>
	  `;
	$(dom).html(htmlString);
  }
    // 弹窗填充数据-功能配置
function fillDialog(objList) {
	Object.entries(objList).map(([key, res]) => {
	  let prefix = key === "set" ? "set_" : "";
	  // 设置覆盖阈值复选框
	  if (key === "set") {
		var coverFlagCheckbox = $('#deviceConfForm [name="coverFlag"]');
		coverFlagCheckbox.prop("checked", res.check);
	  }
	  if (key !== "monitor") {
		// 设置各级别的阈值和告警等级
		res.list.forEach(function (item, index) {
		  var level = index + 1;
		  $('#deviceConfForm [name="' + prefix + "race" + level + '"]').val(
			item.threshold
		  );
		  $('#deviceConfForm [name="' + prefix + "alarm" + level + '"]').val(
			item.alarmLevel
		  );
		});

		// 设置持续时间下拉列表
		$('#deviceConfForm [name="' + prefix + 'Duration"]').val(
		  res.duration || 0
		);
	  }
	  // 智能等待
	  if (key === "monitor") {
		var coverFlagCheckbox = $('#deviceConfForm [name="monitorFlag"]');
		coverFlagCheckbox.prop("checked", res.monitorFlag);
		$('#deviceConfForm [name="monitorTime"]').val(res.monitorTime || 0);
		$('[name="monitorTime"]').attr("disabled", res.monitorFlag ? false : true);
	  }
	});
  }
    	// 功能配置
	$('.deviceConfS').off('click').on('click', function(event) {
    event.preventDefault(); // 阻止默认的表单提交行为
    let type = this.dataset.type
    // status -> 智能等待; threshold -> 正常; 其他不能点
    let valueSort = "asc"
    // desc

    if (type === 'threshold') {
      // 获取表单字段的值
      var formData = $('fieldset#coverConf :input').serializeArray();

      // 遍历字段值检查是否为空
      var isFormFilled = true;
      $.each(formData, function(index, field) {
        if (field.value === '') {
          isFormFilled = false;
          return false; // 提前退出循环
        }
      });

      if (!isFormFilled) {
        return toastr.info('请填写当前表单数据');
      }
    }

    const elements = $("[name^='set_']");
    const result = $.map(elements, function(element) {
      const { name, value } = element;
      if (name !== 'set_Duration') {
        const number = parseInt(name.replace(/\D/g, ""));
        return { name, value, number };
      }
    });

    const groups = {};

    result.forEach(({ name, value, number }) => {
      groups[number] = groups[number] || [];
      groups[number].push({ name, value });
    });

    const ThresholdLevel = Object.values(groups).map(arr => ({
      threshold: arr.find(({ name }) => name.startsWith(`set_race`))?.value ?? null,
      alarmLevel: arr.find(({ name }) => name.startsWith(`set_alarm`))?.value ?? null
    }));


    ThresholdLevel.sort((a, b) => {
      if (valueSort === "asc") {
        return a.threshold - b.threshold;
      } else if (valueSort === "desc") {
        return b.threshold - a.threshold;
      } else {
        return 0; // 如果 valueSort 不是 "asc" 或 "desc"，保持原始顺序
      }
    });

    let threshold = ThresholdLevel.length > 0 && Array.from(ThresholdLevel, ({ threshold }) => threshold).join('|')
    let alarmLevel = ThresholdLevel.length > 0 && Array.from(ThresholdLevel, ({ alarmLevel }) => alarmLevel).join('|')
    var coverFlag = $('input[name="coverFlag"]').prop('checked');
    var monitorFlag = $('input[name="monitorFlag"]').prop('checked');
    var setDuration = $("[name='set_Duration']").val()
    var monitorTime = $("[name='monitorTime']").val()

    if (type === 'status') {
      if (!(monitorTime)) {
        return toastr.info('请填写当前表单数据');
      }
    }

    let dataJson = {
      neServiceItemId: neServiceItemID,
      userName: user.usersLoginName,
      setValue: type === 'status' ? '' : threshold,
      valueSort,
      setAlarmLevel: type === 'status' ? '' : alarmLevel,
      setDuration: parseInt(setDuration),
      coverFlag,
      monitorFlag,
    //   monitorTime: monitorTime
      monitorTime: parseInt(monitorTime)
    }

    $.ajax({
      type: "POST",
      data: JSON.stringify(dataJson),
      headers: {"Content-Type": "application/json;charset=UTF-8"},
      url: Util.api + "/ne/thresholdAndMonitor",
      beforeSend: function (request) {
          request.setRequestHeader("Authorization", token);
      },
      dataType: "json",
      success: function (res) {
        if (res.status !== 0) {
          return toastr.error(res.msg);
        }
        $("#" + subgrid_table_id).trigger("reloadGrid");
        $('#table_list_2').trigger("reloadGrid");
        toastr.success(res.msg);
        $('#deviceConf').modal('hide');
      }
    });
  })

  $('input[name="monitorFlag"]').off('click').on('click',function(ev){
	let checked = ev.target.checked
	if(!checked){
		$('[name="monitorTime"]').val(0)
		$('[name="monitorTime"]').attr("disabled","disabled");
	}else {
		$('[name="monitorTime"]').attr("disabled",false);
	}
  })

	  // status -> 智能等待; threshold -> 正常; 其他不能点
    $("#deviceConf")
    .off("shown.bs.modal")
    .on("shown.bs.modal", function (event) {
      event.preventDefault(); // 阻止默认的表单提交行为
      var button = $(event.relatedTarget); // 触发弹窗的按钮元素
      var type = button.data("type"); // 根据轮询系统类型弹窗显示不同内容
      neServiceItemID = button.data("id"); // 获取按钮的 data-id 属性值
      $(".deviceConfS").attr("data-type", type);

      generateConf("#defaultConf");
      generateConf("#coverConf", 1);
      $("#defaultConf").show();
      $("#coverConf").show();
      if (type === "status") {
      $("#defaultConf").hide();
      $("#coverConf").hide();
      }

      $('#coverConf input[type="text"]').on("input", function () {
      var inputValue = $(this).val();
      if (isNaN(parseInt($(this).val(), 10))) {
        toastr.error("表单格式输入不正确，请输入具体的数值");
      }

      // 检查是否为有效数值
      $(this).val(inputValue.replace(/[^\d]/g, ""));
      });

      $.ajax({
      type: "GET",
      url: Util.api + "/ne/thresholdAndMonitor/" + neServiceItemID,
      beforeSend: function (request) {
        request.setRequestHeader("Authorization", token);
      },
      async: false,
      dataType: "json",
      success: function (res) {
        if (res.status !== 0) {
        return toastr.error(res.msg);
        }
        const deftThresholdLevel = res.data.deftThresholdLevel;
        const setThresholdLevel = res.data.setThresholdLevel;
        let list = {
        monitor: {
          monitorFlag: res.data.monitorFlag,
          monitorTime: res.data.monitorTime,
        },
        };
        // let alarmLevel = deftThresholdLevel.map(function(item) {
				// 	return {
				// 		alarmLevel: item.alarmLevel
				// 	}
				// })
        if (type === "threshold") {
        Object.assign(list, {
          def: {
          list: deftThresholdLevel,
          duration: res.data.deftDuration,
          },
          set: {
          list: setThresholdLevel.length ? setThresholdLevel : deftThresholdLevel,
          check: res.data.coverFlag,
          // duration: res.data.setDuration,
          duration: setThresholdLevel.length ? res.data.setDuration:res.data.deftDuration
          },
        });
        }
        // 功能配置填充
        fillDialog(list);
      },
      });
    });
    // 功能配置数据-------------------End--------------------------------------------------

})
if (Util.getSessionInfo("netServer") == '服务器状态') {
  var smartMonitorBtn = Util.getSessionInfo("smartbtnshow1")
} else {
  var smartMonitorBtn = Util.getSessionInfo("smartbtnshow")
}
// var smartMonitorBtn = Util.getSessionInfo("smartbtnshow")
// getBusiness()
function getBusiness() {
	var customerID = Util.getSessionInfo("curSelectedCustom").customerID;
	var customNo = Util.getSessionInfo("customNo");
	if (customerID!==customNo) {
		top.location = Util.jumpApi + "/static/index.html#homePage?ver=" + getVersions();
		Util.clearSessionStorage('customNo')
	}
}
var functionConfiguration
function ConfigurationButton(cellvalue, options, cell) {
  if (user.language == 'zh') {
    functionConfiguration="功能配置";
 } else {
    functionConfiguration="Configuration";
 }
	var delOneButton
  var styleh =`${smartMonitorBtn?'display:block':''}`
	if(cell.itemType !== 'static'){
	    return delOneButton = '<button type="button" ' + (cell.neServiceItemId && cell.itemType !== 'static' ? '' : 'disabled') + ' class="btn btn-primary btn-config smartMonitorBtn" data-toggle="modal" data-target="#deviceConf" data-type="' + cell.itemType + '" data-id="' + cell.neServiceItemId + `" style="${styleh}">${functionConfiguration}</button>`;
	}else {
		return delOneButton='';

	}
}
function ConfigurationButton1(cellvalue, options, cell) {
  if (user.language == 'zh') {
    functionConfiguration="功能配置";
 } else {
    functionConfiguration="Configuration";
 }
  var styleh =`${smartMonitorBtn?'display:block':''}`
  var delOneButton
	    return delOneButton = '<button type="button"  class="btn btn-primary btn-config smartMonitorBtn" data-toggle="modal" data-target="#deviceConf" data-type="' + 'status' + '" data-id="' + cell.id + `" style="${styleh}">${functionConfiguration}</button>`;

}
// 永久抑制
function isActive(id, status) {
	var token = Util.getSessionInfo("user").token;
  let dataJson = {
    neId:Util.getSessionInfo("ciID"),
    trapSnmpId:id,
    permanent:status?1:0,
  }
  $.ajax({
    type: "POST",
    data: dataJson,
		url: Util.api + `/trap/changeSuppressingStatus`,
		beforeSend: function (request) {
			request.setRequestHeader("Authorization", token);
		},
		success: function (res) {
			if (res.status == 0) {
				$("#table_list_4").trigger("reloadGrid");
        status?toastr.success('永久抑制开启！'):toastr.error('永久抑制关闭！')
			} else {
				toastr.error(res.msg)
			}
		},
		dataType: "json"
	})

}

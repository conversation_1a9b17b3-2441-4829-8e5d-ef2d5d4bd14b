<!doctype html>

<title>CodeMirror: Handlebars mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/mode/simple.js"></script>
<script src="../../addon/mode/multiplex.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="../xml/xml.js"></script>
<script src="handlebars.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">HTML mixed</a>
  </ul>
</div>

<article>
<h2>Handlebars</h2>
<form><textarea id="code" name="code">
{{> breadcrumbs}}

{{!--
  You can use the t function to get
  content translated to the current locale, es:
  {{t 'article_list'}}
--}}

<h1>{{t 'article_list'}}</h1>

{{! one line comment }}

{{{propertyContainingRawHtml}}}

{{#each articles}}
  {{~title}}
  <p>{{excerpt body size=120 ellipsis=true}}</p>

  {{#with author}}
    written by {{first_name}} {{last_name}}
    from category: {{../category.title}}
    {{#if @../last}}foobar!{{/if}}
  {{/with~}}

  {{#if promoted.latest}}Read this one! {{else}} This is ok! {{/if}}

  {{#if @last}}<hr>{{/if}}
{{/each}}

{{#form new_comment}}
  <input type="text" name="body">
{{/form}}

</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: {name: "handlebars", base: "text/html"}
      });
    </script>
    
    <p>Handlebars syntax highlighting for CodeMirror.</p>

    <p><strong>MIME types defined:</strong> <code>text/x-handlebars-template</code></p>

    <p>Supported options: <code>base</code> to set the mode to
    wrap. For example, use</p>
    <pre>mode: {name: "handlebars", base: "text/html"}</pre>
    <p>to highlight an HTML template.</p>
</article>

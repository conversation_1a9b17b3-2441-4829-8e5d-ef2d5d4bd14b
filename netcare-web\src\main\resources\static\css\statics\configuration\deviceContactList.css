.phoneTable div,.emailTable div,.smsTable div,.notifyTable div,.skillTable div{
    height: 36px;
    line-height: 36px;
    border: 1px solid #ddd;
    margin-left: -2px;
}
.phoneTable .phoneTop,.emailTable .emailTop,.smsTable .smsTop,.notifyTable .notifyTop,.skillTable .skillTop{
    background-color: #f5f5f5;
}
.phoneTable .phoneTitle,.emailTable .emailTitle,.smsTable .smsTitle,.notifyTable .notifyTitle,.skillTable .skillTitle {
    font-size: 14px;
    padding-left:10px;
    background: #fcfcfc;
}
.active{
    background-color: #ecf5ff;
}
.phoneTable .phoneAct,.emailTable .emailAct,.smsTable .smsAct,.notifyTable .notifyAct,.skillTable .skillAct{
    height: 82px;
    margin: 0 !important;
    margin-left: -2px !important;
    width: 284px;
    padding: 0;
    line-height: 36px;
    border: 1px solid #ddd;
    cursor: pointer;
}
.saveAll{
    width: 51px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    font-weight: 400;
    color: #fff;
    font-size:14px;
    background: #3E97FF;
    margin-left: 14px;
    margin-bottom: 10px;
}
.form-horizontal .form-group {
    margin: 0;
    margin-top: 8px;
    width: 450px;
    margin-right: 10px;
}
.form-search{
    height: 25px;
}
.form-horizontal{
    margin-bottom: 20px;
}
.ibox-content{
    border: none;
}
.phoneIcon,.emailIcon,.smsIcon,.notifyIcon,.skillIcon{
    float: left;
    border: none !important;
    height: 153px !important;
    line-height: 153px !important;
    font-size: 21px;
    cursor: pointer;
}
.phoneAct option,.emailAct option,.smsAct option,.notifyAct option,.skillAct option{
    padding:5px;
}
.phoneDetail{
    float:left;
    padding-left: 0 !important;
}


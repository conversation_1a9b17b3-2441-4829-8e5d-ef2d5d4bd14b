.col-sm-4 {
	padding-left: 0;
}
.blk {
	display: block !important;
}

.collapsed .titleTool {
	width: 100%;
	height: 31px;
	line-height: 31px;
	background: #f3f3f4;
	padding-left: 10px;
	font-weight: 600;
}

.collapsed .contColla {
	color: #f00;
	padding-left: 8px;
	font-size:14px;
	list-style: none;
	line-height: 20px;
}
.contColla li {
	cursor: pointer;
}
.contColla li:hover {
	color: deepskyblue;
}
.contPing {
	height: 300px;
}
.contPing p {
	line-height: 36px;
}


.conBot {
	position: absolute;
	top: 221px;
}
.collapsed1 .titleTool1 {
	width: 100%;
	height: 31px;
	line-height: 31px;
	background: #f3f3f4;
	font-weight: 600;
	border: 1px solid #ccc;
	border-left: none;
	border-right: none;
	padding: 0;
	margin: 0;
	padding-left: 10px;
}

.expanded1 {
	width: 100%;
	height: 30px;
	border: 1px solid #ccc;
	position: absolute;
	top: 545px;
	z-index: 998;
	background: #fff;
}
.collapsed1 {
	width: 100%;
	height: 400px;
	border: 1px solid #ccc;
	border-top: none;
	position: absolute;
	top: 221px;
	z-index: 999;
	background: #fff;
}
.collapsed1 .res-table {
	width: 100%;
	height: auto;
	padding: 0;
	margin: 0;
	overflow-y: scroll;
}
.res-content {
	width: 100%;
	height: 48px;
	padding: 0;
	margin: 0;
}
.res-content .res-left {
	width: 20%;
	height: 48px;
	background: #d6d6d6;
	text-align: center;
	line-height: 48px;
	padding: 0;
	margin: 0;
}
.res-content .res-right {
	width: 80%;
	height: 48px;
	background: #ecefea;
	text-align: center;
	line-height: 48px;
	padding: 0;
	margin: 0;
}
/*InterfaceInfo*/
.res-content .InterfaceInfo-top,.res-content .InterfaceInfo-content,.res-content .Traffic-top,.res-content .Packets-top {
	width: 100%;
	height: 42px;
}
.InterfaceInfo-top div {
	float: left;
	height: 42px;
	width: 10%;
	text-align: center;
	line-height: 42px;
	background: #59616c;
	color: #000;
}
.Traffic-top div {
	float: left;
	height: 42px;
	width: 25%;
	text-align: center;
	line-height: 42px;
	background: #59616c;
	color: #000;
}
.Packets-top div {
	float: left;
	height: 42px;
	width: 10%;
	text-align: center;
	line-height: 42px;
	background: #59616c;
	color: #000;
}
.Traffic-content div {
	float: left;
	height: 42px;
	width: 25%;
	text-align: center;
	line-height: 42px;
	background: #d6d6d6;
	color: #000;
}
.InterfaceInfo-content div {
	padding:5px 0;
	padding: 0;
	float: left;
	height: 60px;
	width: 10%;
	text-align: center;
	/* line-height: 42px; */
	background: #d6d6d6;
	color: #000;
	word-break: break-word;
	display: flex;
	align-items: center;
	justify-content: center;
}
.Packets-content div {
	float: left;
	height: 42px;
	width: 10%;
	text-align: center;
	line-height: 42px;
	background: #d6d6d6;
	color: #000;
}
.InterfaceInfo-content .Up .color {
	background: #5CB85C;
}
.InterfaceInfo-content .Down .color {
	background: #D9534F;
}
.InterfaceInfo-content .Full,.InterfaceInfo-content .Auto {
	background: #5CB85C;
}
.InterfaceInfo-content .Unknown {
	background: #999999 ;
}

.InterfaceInfo-content .Half {
	background: #9370D8 ;
}
.color{
	width:80%;
	height:30px;
	line-height: 30px;
	border-radius: 10px;
	color:#fff
}
.InterfaceInfo-top .ifAlias,.InterfaceInfo-content .ifAlias {
	width: 16%;
}
.InterfaceInfo-top .ifIndex, .InterfaceInfo-content .ifIndex{
    width:5%
}
.InterfaceInfo-top .ifType, .InterfaceInfo-content .ifType{
    width:5%
}
.InterfaceInfo-top .ifAdminStatus, .InterfaceInfo-content .Up{
    width:8%
}
.InterfaceInfo-top .ifOperStatus, .InterfaceInfo-content .Down{
    width:8%
}
.InterfaceInfo-top .duplex1, .InterfaceInfo-content .duplex1{
    width:8%
}
.expanded {
	width: 30px;
	height: 570px;
	position: absolute;
	top: -0;
	left: 0;
	z-index: 1;
	border: 1px solid #CCCCCC;
	background: #fff;
	border-top: none;
	border-bottom: none;
}
.collapsed {
	width: 100%;
	height: 570px;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 2;
	border: 1px solid #CCCCCC;
	border-top: none;
	border-bottom: none;
}
.rightCont {
	width: 100%;
	height: 570px;
	border-right: 1px solid #ccc;
}
.titleTool2 {
	width: 100%;
	height: 31px;
	line-height: 31px;
	background: #f3f3f4;
	padding-left: 10px;
	font-weight: 600;
	margin-bottom: 0px;
}
#listT {
	border-color: #ccc;
	margin: 20px;
	text-align: center;
}
#rolesTable {
	text-align: center;
	border-color: #ccc;
}
.ui-jqgrid {
	border-right: none;
	border-left: none;
	border-bottom: none;
}
.ui-jqgrid tr.jqgrow td {
	color: blue;
    font-weight: normal;
    overflow: hidden;
    white-space: pre;
    height: 22px;
    padding: 0 2px 0 2px;
    border-top-width: 1px;
    border-top-color: inherit;
    border-top-style: solid;
    cursor: pointer;
}
.ui-jqgrid tr.jqgrow td:hover {
	color: #f00;
}

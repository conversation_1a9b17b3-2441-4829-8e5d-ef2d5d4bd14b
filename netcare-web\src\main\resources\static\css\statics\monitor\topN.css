.leftTitle{
    height: 36px;
    background: #ddd;
    line-height: 36px;
    font-weight: 700;
    margin-left: -15px;
    padding-left: 20px;
}
/* .statisticLeft{
    border: 1px solid #ddd;
} */

.statisticLeft .form-group{
    margin-top: 15px;
    margin-bottom: 60px;
    padding-right: 10px;
}
.statisticLeft .form-group label{
    line-height: 34px;
}
.statisticLeft .search{
    margin-top:10px;
    margin-left: 185px;
}
.statisticRight .ibox{
    margin-bottom: 0 !important;
}
#searchTime a,#dataAnalysis a{
    text-decoration: underline;
    margin: 10px;
}
.echarts{
    height: 347px;
    background-color: #00a2d4;
}
.modal-bod{
    height: 350px;
}
.modal-dial{
    width: 950px;
}
.time{
    padding: 0 50px;
}
.time a{
    margin: 5px;
    text-decoration: underline;
}
.btn-cli{
    text-align: right;
}
.dataSourse a{
    text-decoration: underline;
    font-size: 16px;
}
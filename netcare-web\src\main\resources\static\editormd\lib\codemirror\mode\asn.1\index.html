﻿<!doctype html>

<title>CodeMirror: ASN.1 mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="asn.1.js"></script>
<style>
    .CodeMirror {
        border-top: 1px solid black;
        border-bottom: 1px solid black;
    }
</style>
<div id=nav>
    <a href="https://codemirror.net/5"><h1>CodeMirror</h1>
        <img id=logo src="../../doc/logo.png" alt="">
    </a>

    <ul>
        <li><a href="../../index.html">Home</a>
        <li><a href="../../doc/manual.html">Manual</a>
        <li><a href="https://github.com/codemirror/codemirror5">Code</a>
    </ul>
    <ul>
        <li><a href="../index.html">Language modes</a>
        <li><a class=active href="http://en.wikipedia.org/wiki/Abstract_Syntax_Notation_One">ASN.1</a>
    </ul>
</div>
<article>
    <h2>ASN.1 example</h2>
    <div>
        <textarea id="ttcn-asn-code">
 --
 -- Sample ASN.1 Code
 --
 MyModule DEFINITIONS ::=
 BEGIN

 MyTypes ::= SEQUENCE {
     myObjectId   OBJECT IDENTIFIER,
     mySeqOf      SEQUENCE OF MyInt,
     myBitString  BIT STRING {
                         muxToken(0),
                         modemToken(1)
                  }
 }

 MyInt ::= INTEGER (0..65535)

 END
        </textarea>
    </div>

    <script>
        var ttcnEditor = CodeMirror.fromTextArea(document.getElementById("ttcn-asn-code"), {
            lineNumbers: true,
            matchBrackets: true,
            mode: "text/x-ttcn-asn"
        });
        ttcnEditor.setSize(400, 400);
        var mac = CodeMirror.keyMap.default == CodeMirror.keyMap.macDefault;
        CodeMirror.keyMap.default[(mac ? "Cmd" : "Ctrl") + "-Space"] = "autocomplete";
    </script>
    <br/>
    <p><strong>Language:</strong> Abstract Syntax Notation One
        (<a href="http://www.itu.int/en/ITU-T/asn1/Pages/introduction.aspx">ASN.1</a>)
    </p>
    <p><strong>MIME types defined:</strong> <code>text/x-ttcn-asn</code></p>

    <br/>
    <p>The development of this mode has been sponsored by <a href="http://www.ericsson.com/">Ericsson
    </a>.</p>
    <p>Coded by Asmelash Tsegay Gebretsadkan </p>
</article>

